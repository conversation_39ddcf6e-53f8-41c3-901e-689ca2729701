#pragma once

#include "nr_plugin_interface.h"
#include "nr_plugin_space_screen_types.h"
#include "nr_plugin_glasses_types.h"
NR_DECLARE_INTERFACE(NRSpaceScreenInterface) {

    NRPluginResult(NR_INTERFACE_API *GetDpInputMode)(
        NRPluginHandle handle,
        NRDpInputMode * out_mode,
        NRDpInputSubMode * out_sub_mode
    );

    NRPluginResult(NR_INTERFACE_API *SetDpInputMode)(
        NRPluginHandle handle,
        NRDpInputMode input_mode,
        NRDpInputSubMode input_sub_mode
    );
	/*
		<ifunction name="GetLocalPerceptionType">
		<param name="handle" type="NRPluginHandle"/>
		<param name="out_type" type="NRPerceptionType *"/>
		</ifunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetCanvasDiagonalSize)(
        NRPluginHandle handle,
        float * out_size
    );

    NRPluginResult(NR_INTERFACE_API *UpdateCanvasSize)(
        NRPluginHandle handle,
        NROperationType operation_type,
        NRStepType step_type
    );
	/*
		<ifunction name="DecreaseCanvasSize">
		<param name="handle" type="NRPluginHandle"/>
		<param name="step_type" type="NRStepType"/>
		</ifunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetCanvasDepth)(
        NRPluginHandle handle,
        float * out_depth
    );

    NRPluginResult(NR_INTERFACE_API *UpdateCanvasDepth)(
        NRPluginHandle handle,
        NROperationType operation_type,
        NRStepType step_type
    );
	/*
		<ifunction name="DecreaseCanvasDepth">
		<param name="handle" type="NRPluginHandle"/>
		<param name="step_type" type="NRStepType"/>
		</ifunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetSceneMode)(
        NRPluginHandle handle,
        NRSceneMode * out_mode
    );
	/*
		
		正常：16:9
		超宽屏：32:9 （目前仅支持mono模式）
		模式调节+超宽屏
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetSpaceMode)(
        NRPluginHandle handle,
        NRSpaceMode * out_space_mode
    );

    NRPluginResult(NR_INTERFACE_API *SetSpaceMode)(
        NRPluginHandle handle,
        NRSpaceMode space_mode
    );
	/*
		
		小窗模式
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetThumbnailEnable)(
        NRPluginHandle handle,
        bool * out_thumbnail_enable
    );

    NRPluginResult(NR_INTERFACE_API *SetThumbnailEnable)(
        NRPluginHandle handle,
        bool thumbnail_enable
    );
	/*
		
		小窗位置
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetThumbnailPositionType)(
        NRPluginHandle handle,
        NRThumbnailPositionType * out_thumbnail_position
    );

    NRPluginResult(NR_INTERFACE_API *SetThumbnailPositionType)(
        NRPluginHandle handle,
        NRThumbnailPositionType thumbnail_position
    );
	/*
		
		跟随防抖
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetEisEnable)(
        NRPluginHandle handle,
        bool * out_eis_enable
    );

    NRPluginResult(NR_INTERFACE_API *SetEisEnable)(
        NRPluginHandle handle,
        bool eis_enable
    );
	/*
		
		瞳距调节
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetPupilLevelCount)(
        NRPluginHandle handle,
        int32_t * out_level_count
    );

    NRPluginResult(NR_INTERFACE_API *GetPupilLevel)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *SetPupilLevel)(
        NRPluginHandle handle,
        int32_t level
    );
	/*
		
		进入/退出瞳距调节页面
		
	*/
    NRPluginResult(NR_INTERFACE_API *StartPupilAdjust)(
        NRPluginHandle handle
    );

    NRPluginResult(NR_INTERFACE_API *StopPupilAdjust)(
        NRPluginHandle handle
    );
	/*
		
		超宽屏开关，只为兼容用，建议用GetUltraWideMode/SetUltraWideMode
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetUltraWideEnable)(
        NRPluginHandle handle,
        bool * out_ultra_wide_enable
    );

    NRPluginResult(NR_INTERFACE_API *SetUltraWideEnable)(
        NRPluginHandle handle,
        bool ultra_wide_enable
    );
	/*
		
		画面校准
		
	*/
    NRPluginResult(NR_INTERFACE_API *Recenter)(
        NRPluginHandle handle
    );
	/*
		
		是否启用6dof
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetTranslationEnable)(
        NRPluginHandle handle,
        bool * out_translation_enable
    );

    NRPluginResult(NR_INTERFACE_API *SetTranslationEnable)(
        NRPluginHandle handle,
        bool translation_enable
    );
	/*
		
		超宽屏模式切换
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetUltraWideMode)(
        NRPluginHandle handle,
        NRUltraWideMode * out_ultra_wide_mode
    );

    NRPluginResult(NR_INTERFACE_API *SetUltraWideMode)(
        NRPluginHandle handle,
        NRUltraWideMode ultra_wide_mode
    );
};

NR_REGISTER_INTERFACE_GUID(0xF7A1ADB2D9E64AA5ULL, 0xB29633FA4B464BA9ULL,
                            NRSpaceScreenInterface)

