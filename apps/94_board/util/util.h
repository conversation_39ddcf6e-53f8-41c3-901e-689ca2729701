#ifndef UTIL_H
#define UTIL_H

#include <time.h>
#include <sys/time.h>

#include <stdint.h>
#include <string>
#include <vector>

// Forward declaration
namespace Json {
    class Value;
}

//! Helper define to make code more readable.
#define OS_NS_PER_SEC (1000000000)
#define OS_MSEC_PER_SEC (1000000)
#define OS_NS_PER_MSEC (1000000)
#define OS_NS_PER_USEC (1000)
#define F_TIME_1MS_IN_NS (1000000)

/*!
 * @brief Convert a timespec struct to nanoseconds.
 *
 * Note that this just does the value combining, no adjustment for epochs is performed.
 */
static inline uint64_t FTimespecToNs(const struct timespec *spec)
{
	uint64_t ns = 0;
	ns += (uint64_t)spec->tv_sec * OS_NS_PER_SEC;
	ns += (uint64_t)spec->tv_nsec;
	return ns;
}

/*!
 * @brief Return a monotonic clock in nanoseconds.
 */
static inline uint64_t FMonotonicGetNs(void)
{
	struct timespec ts;
	int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
	if (ret != 0) {
		return 0;
	}

	return FTimespecToNs(&ts);
}


/* need to zero out the ticklist array before starting */
/* average will ramp up until the buffer is full */
/* returns average calls per second over the samples last calls */
/* no print when print_interval_sec is 0 */
class CallCounter
{
public:
    CallCounter(std::string name, uint32_t samples) : name_(name), samples_(samples),
                                                      tick_list_(samples, 0) {}
    void Update();

private:
    std::string name_;

    int samples_;
    int tick_index_ = 0;
    uint64_t tick_sum_ = 0;
    std::vector<uint64_t> tick_list_;
    uint64_t last_tick_ = 0;

    uint64_t last_print_timestamp_;
    const uint32_t PRINT_INTERVAL_SEC = 3;
};

void GenVOMaskFromFile(char* mask_data[2]);

// Structure to hold mesh data for interpolation
struct MeshData {
    int num_rows;
    int num_cols;
    std::vector<float> grid_x;
    std::vector<float> grid_y;
    std::vector<float> pt_x;
    std::vector<float> pt_y;
};

// Function to generate pixel-wise distortion values from mesh data
bool GeneratePixelwiseDistortion(const MeshData& mesh,
                                int display_width, int display_height,
                                std::vector<float>& pixel_dis_pt_x,
                                std::vector<float>& pixel_dis_pt_y);

// Helper function to extract mesh data from JSON
bool ExtractMeshDataFromJson(const Json::Value &display_data, MeshData& mesh);

// Complete function to generate pixel-wise distortion from config file
bool GeneratePixelwiseDistortionFromConfig(const std::string& config_path,
                                          int display_width, int display_height,
                                          std::vector<float>& left_dis_pt_x,
                                          std::vector<float>& left_dis_pt_y,
                                          std::vector<float>& right_dis_pt_x,
                                          std::vector<float>& right_dis_pt_y);

#endif