#include <heron/model/permanent_config.h>
#include <heron/model/model_manager.h>

#include <heron/interface_provider/file.h>
#include <heron/interface_provider/generic.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <iomanip> // For std::fixed and std::setprecision

using namespace heron;
using namespace heron::interface_provider;

static void ParseJsonFloatArray(std::vector<float> &out_array, const Json::Value &json_array, const std::string &array_name)
{
    out_array.clear();
    uint32_t array_size = json_array.size();
    std::stringstream ss;
    ss << "[";
    // Set fixed-point notation and 2 decimal precision
    ss << std::fixed << std::setprecision(2);
    for (uint32_t i = 0; i < array_size; i++)
    {
        out_array.emplace_back(json_array[i].asFloat());
        ss << out_array[i];
        if (i != array_size - 1)
            ss << ", ";
        else
            ss << "]";
    }
    HERON_LOG_DEBUG("{} parsed: {}", array_name, ss.str());
}

void PermanentConfig::ParseCanvasPanel(CanvasPanel &panel, const Json::Value &root)
{
    if (root.isMember("min_canvas_depth_meters"))
    {
        panel.min_canvas_depth_meters_ = root["min_canvas_depth_meters"].asFloat();
        HERON_LOG_DEBUG("min_canvas_depth_meters parsed: {:.2f}", panel.min_canvas_depth_meters_);
    }
    if (root.isMember("max_canvas_depth_meters"))
    {
        panel.max_canvas_depth_meters_ = root["max_canvas_depth_meters"].asFloat();
        HERON_LOG_DEBUG("max_canvas_depth_meters parsed: {:.2f}", panel.max_canvas_depth_meters_);
    }
    if (root.isMember("canvas_depth_step_meters"))
    {
        panel.canvas_depth_step_meters_ = root["canvas_depth_step_meters"].asFloat();
        HERON_LOG_DEBUG("canvas_depth_step_meters parsed: {:.2f}", panel.canvas_depth_step_meters_);
    }
    if (root.isMember("extended_canvas_depth_step_meters"))
    {
        panel.extended_canvas_depth_step_meters_ = root["extended_canvas_depth_step_meters"].asFloat();
        HERON_LOG_DEBUG("extended_canvas_depth_step_meters parsed: {:.2f}", panel.extended_canvas_depth_step_meters_);
    }
    if (root.isMember("canvas_diagonal_fov_factor_array"))
    {
        const Json::Value &fov_factor_array = root["canvas_diagonal_fov_factor_array"];
        ParseJsonFloatArray(panel.canvas_diagonal_fov_factor_array_, fov_factor_array, "canvas_diagonal_fov_factor_array");
    }
    if (root.isMember("default_canvas_fov_index"))
    {
        panel.default_canvas_fov_index_ = root["default_canvas_fov_index"].asUInt();
        HERON_LOG_DEBUG("default_canvas_fov_index parsed: {}", panel.default_canvas_fov_index_);
    }
    if (root.isMember("extended_canvas_diagonal_fov_factor_step"))
    {
        panel.extended_canvas_diagonal_fov_factor_step_ = root["extended_canvas_diagonal_fov_factor_step"].asFloat();
        HERON_LOG_DEBUG("extended_canvas_diagonal_fov_factor_step parsed: {:.2f}", panel.extended_canvas_diagonal_fov_factor_step_);
    }
    if (root.isMember("maintain_fov_on_depth_change"))
    {
        panel.maintain_fov_on_depth_changed_ = root["maintain_fov_on_depth_change"].asBool();
        HERON_LOG_DEBUG("maintain_fov_on_depth_changed_ parsed: {}", panel.maintain_fov_on_depth_changed_);
    }
}

void PermanentConfig::ParseFlingerDefaultConfig(const std::string &config_string, bool overwrite)
{
    Json::Value root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(config_string);
    if (!parseFromStream(json_builder, json_stream, &root, &json_errs))
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, json_errs = {}", json_errs.c_str());
        return;
    }

    if (!root.isMember("flinger") && !overwrite)
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, node flinger not found");
        return;
    }
    const Json::Value &flinger = root["flinger"];
    if (flinger.isMember("default_canvas_depth_meters"))
    {
        canvas_base_depth_meters_ = flinger["default_canvas_depth_meters"].asFloat();
        HERON_LOG_DEBUG("default_canvas_depth_meters parsed: {:.2f}", canvas_base_depth_meters_);
    }
    if (flinger.isMember("presentation_diagonal_fov_degree"))
    {
        presentation_diagonal_fov_degree_ = flinger["presentation_diagonal_fov_degree"].asFloat();
        HERON_LOG_DEBUG("presentation_diagonal_fov_degree parsed: {:.2f}", presentation_diagonal_fov_degree_);
    }
    if (!flinger.isMember("normal"))
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, node normal not found");
        return;
    }
    const Json::Value &normal = flinger["normal"];
    ParseCanvasPanel(normal_, normal);
    if (!flinger.isMember("3D"))
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, node 3D not found");
        return;
    }
    const Json::Value &sbs = flinger["3D"];
    ParseCanvasPanel(sbs_, sbs);
    if (!flinger.isMember("ultra_wide"))
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, node ultra_wide not found");
        return;
    }
    const Json::Value &ultra_wide = flinger["ultra_wide"];
    ParseCanvasPanel(ultra_wide_, ultra_wide);
    if (flinger.isMember("depth_transition_duration_ms"))
    {
        depth_transition_duration_ms_ = flinger["depth_transition_duration_ms"].asFloat();
        HERON_LOG_DEBUG("depth_transition_duration_ms parsed: {:.2f}", depth_transition_duration_ms_);
    }
    // Size
    if (flinger.isMember("fov_factor_transition_duration_ms"))
    {
        size_transition_duration_ms_ = flinger["fov_factor_transition_duration_ms"].asFloat();
        HERON_LOG_DEBUG("fov_factor_transition_duration_ms parsed: {:.2f}", size_transition_duration_ms_);
    }

    // PupilAdjustment
    if (flinger.isMember("pupil_adjustment_pixel_array"))
    {
        const Json::Value &pixel_array = flinger["pupil_adjustment_pixel_array"];
        ParseJsonFloatArray(pupil_adjustment_pixel_array_, pixel_array, "pupil_adjustment_pixel_array");
    }
    if (flinger.isMember("pupil_level_transition_duration_ms"))
    {
        pupil_level_transition_duration_ms_ = flinger["pupil_level_transition_duration_ms"].asFloat();
        HERON_LOG_DEBUG("pupil_level_transition_duration_ms: {:.2f}", pupil_level_transition_duration_ms_);
    }
    // thumbnail
    if (flinger.isMember("thumbnail"))
    {
        const Json::Value &thumbnail_config = flinger["thumbnail"];
        ParseThumbnailConfig(thumbnail_config);
    }
    if (flinger.isMember("auto_ec_adjustment_thresh"))
    {
        const Json::Value &auto_ec_thresh = flinger["auto_ec_adjustment_thresh"];
        auto_ec_adjustment_thresh_[0] = auto_ec_thresh[0].asFloat();
        auto_ec_adjustment_thresh_[1] = auto_ec_thresh[1].asFloat();
        HERON_LOG_DEBUG("auto_ec_adjustment_threash parsed: {},{}", auto_ec_adjustment_thresh_[0], auto_ec_adjustment_thresh_[1]);
    }
    // blank frame color
    if (flinger.isMember("blank_frame_color"))
    {
        const Json::Value &blank_frame_color = flinger["blank_frame_color"];
        blank_frame_color_[0] = blank_frame_color[0].asUInt();
        blank_frame_color_[1] = blank_frame_color[1].asUInt();
        blank_frame_color_[2] = blank_frame_color[2].asUInt();
        HERON_LOG_DEBUG("blank_frame_color parsed: [{},{},{}]", blank_frame_color_[0], blank_frame_color_[1], blank_frame_color_[2]);
    }
}
void PermanentConfig::ParseThumbnailConfig(const Json::Value &root)
{
    if (root.isMember("depth_meters"))
    {
        thumbnail_depth_meters_ = root["depth_meters"].asFloat();
        HERON_LOG_DEBUG("thumbnail_depth_meters parsed: {:.2f}", thumbnail_depth_meters_);
    }
    if (root.isMember("diagonal_size_inches"))
    {
        thumbnail_diagonal_size_inches_ = root["diagonal_size_inches"].asFloat();
        HERON_LOG_DEBUG("thumbnail_diagonal_size_inches parsed: {:.2f}", thumbnail_diagonal_size_inches_);
    }
    if (root.isMember("left_position"))
    {
        Json::Value left_position = root["left_position"];
        thumbnail_left_position_.x() = left_position[0].asFloat();
        thumbnail_left_position_.y() = left_position[1].asFloat();
        HERON_LOG_DEBUG("thumbnail_left_position_ parsed: ({:.2f},{:.2f})", thumbnail_left_position_.x(), thumbnail_left_position_.y());
    }
    if (root.isMember("right_position"))
    {
        Json::Value right_position = root["right_position"];
        thumbnail_right_position_.x() = right_position[0].asFloat();
        thumbnail_right_position_.y() = right_position[1].asFloat();
        HERON_LOG_DEBUG("thumbnail_right_position_ parsed: ({:.2f},{:.2f})", thumbnail_right_position_.x(), thumbnail_right_position_.y());
    }
}

bool PermanentConfig::GenerateUserConfigFileAndDump(const std::string &config_file_full_path)
{
    Json::Value json_root;
    json_root["space_mode_saved"] = space_mode_saved_;
    json_root["thumbnail_position_type_saved"] = thumbnail_position_type_saved_;
    json_root["pupil_level_index_saved"] = pupil_level_index_saved_;
    Json::Value normal_panel;
    normal_panel["canvas_depth_saved"] = normal_.canvas_depth_saved_;
    normal_panel["canvas_size_factor_saved"] = normal_.canvas_size_factor_saved_;
    json_root["normal"] = normal_panel;
    Json::Value sbs_panel;
    sbs_panel["canvas_depth_saved"] = sbs_.canvas_depth_saved_;
    sbs_panel["canvas_size_factor_saved"] = sbs_.canvas_size_factor_saved_;
    json_root["3D"] = sbs_panel;
    Json::Value ultra_wide_panel;
    ultra_wide_panel["canvas_depth_saved"] = ultra_wide_.canvas_depth_saved_;
    ultra_wide_panel["canvas_size_factor_saved"] = ultra_wide_.canvas_size_factor_saved_;
    json_root["ultra_wide"] = ultra_wide_panel;

    Json::StreamWriterBuilder builder;
    std::string jsonString = Json::writeString(builder, json_root);
    FileInterface::GetInstance()->SafeSaveBufferToFile(jsonString.c_str(), jsonString.length(), config_file_full_path.c_str(), config_file_full_path.length());
    return true;
}

bool PermanentConfig::ParseUserConfig(const std::string &config_file_full_path)
{
    HERON_LOG_DEBUG("flinger user config file full path: {}", config_file_full_path);
    uint32_t user_config_json_string_length = 2048;
    char *user_config_json = new char[user_config_json_string_length];
    if (!FileInterface::GetInstance()->SafeReadBufferFromFile(config_file_full_path.c_str(), config_file_full_path.length(), &user_config_json_string_length, user_config_json))
    {
        HERON_LOG_WARN("no UserConfig, using default values.");
        return GenerateUserConfigFileAndDump(config_file_full_path);
    }
    HERON_LOG_DEBUG("flinger user config json str length: {}", user_config_json_string_length);
    std::string user_config_json_string(user_config_json, user_config_json_string_length);
    Json::Value root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(user_config_json_string);
    if (!parseFromStream(json_builder, json_stream, &root, &json_errs))
    {
        HERON_LOG_ERROR("ParseDefaultFlingerConfig error, json_errs = {}", json_errs.c_str());
        return false;
    }
    if (root.isMember("space_mode_saved"))
    {
        space_mode_saved_ = (SpaceMode)root["space_mode_saved"].asUInt();
        HERON_LOG_DEBUG("space_mode_saved parsed: {}", space_mode_saved_);
    }
    if (root.isMember("thumbnail_position_type_saved"))
    {
        thumbnail_position_type_saved_ = (ThumbnailPositionType)root["thumbnail_position_type_saved"].asUInt();
        HERON_LOG_DEBUG("thumbnail_position_type_saved parsed: {}", thumbnail_position_type_saved_);
    }
    if (root.isMember("pupil_level_index_saved"))
    {
        pupil_level_index_saved_ = root["pupil_level_index_saved"].asUInt();
        HERON_LOG_DEBUG("pupil_level_index_saved parsed: {}", pupil_level_index_saved_);
    }
    if (root.isMember("normal"))
    {
        const Json::Value &normal = root["normal"];
        normal_.canvas_depth_saved_ = normal["canvas_depth_saved"].asFloat();
        normal_.canvas_size_factor_saved_ = normal["canvas_size_factor_saved"].asFloat();
        HERON_LOG_DEBUG("normal canvas_depth_saved parsed: {:.2f}, canvas_size_factor_saved parsed: {:.2f}", normal_.canvas_depth_saved_, normal_.canvas_size_factor_saved_);
    }
    if (root.isMember("3D"))
    {
        const Json::Value &sbs = root["3D"];
        sbs_.canvas_depth_saved_ = sbs["canvas_depth_saved"].asFloat();
        sbs_.canvas_size_factor_saved_ = sbs["canvas_size_factor_saved"].asFloat();
        HERON_LOG_DEBUG("3D canvas_depth_saved parsed: {:.2f}, canvas_size_factor_saved parsed: {:.2f}", sbs_.canvas_depth_saved_, sbs_.canvas_size_factor_saved_);
    }
    if (root.isMember("ultra_wide"))
    {
        const Json::Value &ultra_wide = root["ultra_wide"];
        ultra_wide_.canvas_depth_saved_ = ultra_wide["canvas_depth_saved"].asFloat();
        ultra_wide_.canvas_size_factor_saved_ = ultra_wide["canvas_size_factor_saved"].asFloat();
        HERON_LOG_DEBUG("ultra_wide canvas_depth_saved parsed: {:.2f}, canvas_size_factor_saved parsed: {:.2f}", ultra_wide_.canvas_depth_saved_, ultra_wide_.canvas_size_factor_saved_);
    }
    delete[] user_config_json;
    return true;
}

bool PermanentConfig::ParseConfigs()
{
    uint32_t default_config_length = 0;
    const char *default_config_str = nullptr;
    if (GenericInterface::GetInstance()->GetAppDefaultConfig(&default_config_str, &default_config_length))
    { // the app default config not only contains app configs but all default configs
        HERON_LOG_DEBUG("app config json str length: {}", default_config_length);
        std::string app_default_config_json_string(default_config_str, default_config_length);
        ParseFlingerDefaultConfig(app_default_config_json_string);
    }
    if (GenericInterface::GetInstance()->GetDeviceDefaultConfig(&default_config_str, &default_config_length))
    { // contains device-specific configs that overwrites default configs
        HERON_LOG_DEBUG("device config json str length: {}", default_config_length);
        std::string device_default_config_json_string(default_config_str, default_config_length);
        ParseFlingerDefaultConfig(device_default_config_json_string, true);
    }
    const char *user_config_directory = nullptr;
    uint32_t directory_size = 0;
    FileInterface::GetInstance()->GetUserConfigDirectory(&user_config_directory, &directory_size);
    std::string user_config_file_path(user_config_directory, directory_size);
    ParseUserConfig(user_config_file_path + "/" + model::ModelManager::USER_CONFIG_FILE_NAME);
    return true;
}

bool PermanentConfig::UpdateUserConfigFileAndDump()
{
    const char *user_config_directory = nullptr;
    uint32_t directory_size = 0;
    FileInterface::GetInstance()->GetUserConfigDirectory(&user_config_directory, &directory_size);
    std::string user_config_file_path(user_config_directory, directory_size);
    return GenerateUserConfigFileAndDump(user_config_file_path + "/" + model::ModelManager::USER_CONFIG_FILE_NAME);
}

void PermanentConfig::ResetUserConfigs()
{
    const char *user_config_directory = nullptr;
    uint32_t directory_size = 0;
    FileInterface::GetInstance()->GetUserConfigDirectory(&user_config_directory, &directory_size);
    std::string user_config_file_path(user_config_directory, directory_size);
    user_config_file_path = user_config_file_path + "/" + model::ModelManager::USER_CONFIG_FILE_NAME;
    FileInterface::GetInstance()->SafeRemoveFile(user_config_file_path.c_str(), user_config_file_path.length());
}

bool PermanentConfig::GetNearestFovFactorFromArrayLargerThan(SpaceMode space_mode, DpInputMode dp_input_mode, float curr_factor, float *out_factor)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    if (auto it = std::upper_bound(panel->canvas_diagonal_fov_factor_array_.begin(), panel->canvas_diagonal_fov_factor_array_.end(), curr_factor + EPS); it != panel->canvas_diagonal_fov_factor_array_.end())
    {
        *out_factor = *it;
        HERON_LOG_DEBUG("in curr_factor{}, out_factor{}", curr_factor, *out_factor);
        return true;
    }
    HERON_LOG_ERROR("{} error.", __FUNCTION__);
    return false;
}

bool PermanentConfig::GetNearestFovFactorFromArrayLessThan(SpaceMode space_mode, DpInputMode dp_input_mode, float curr_factor, float *out_factor)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    if (auto it = std::lower_bound(panel->canvas_diagonal_fov_factor_array_.begin(), panel->canvas_diagonal_fov_factor_array_.end(), curr_factor - EPS); it != panel->canvas_diagonal_fov_factor_array_.begin())
    {
        *out_factor = *--it;
        HERON_LOG_DEBUG("de curr_factor{}, out_factor{}", curr_factor, *out_factor);
        return true;
    }
    HERON_LOG_ERROR("{} error.", __FUNCTION__);
    return false;
}

float PermanentConfig::GetCanvasDepthSaved(SpaceMode space_mode, DpInputMode dp_input_mode)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    HERON_LOG_DEBUG("{} space:{}, dp_in:{} depth:{}", __FUNCTION__, space_mode, dp_input_mode, panel->canvas_depth_saved_);
    return panel->canvas_depth_saved_;
}
void PermanentConfig::SetCanvasDepth(SpaceMode space_mode, DpInputMode dp_input_mode, float depth)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    HERON_LOG_DEBUG("{} space:{}, dp_in:{} depth:{}", __FUNCTION__, space_mode, dp_input_mode, panel->canvas_depth_saved_);
    panel->canvas_depth_saved_ = depth;
}

float PermanentConfig::GetCanvasSizeFactorSaved(SpaceMode space_mode, DpInputMode dp_input_mode)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    HERON_LOG_DEBUG("{} space:{}, dp_in:{} size_factor:{}", __FUNCTION__, space_mode, dp_input_mode, panel->canvas_size_factor_saved_);
    return panel->canvas_size_factor_saved_;
}
void PermanentConfig::SetCanvasSizeFactor(SpaceMode space_mode, DpInputMode dp_input_mode, float size_factor)
{
    CanvasPanel* panel = GetCurrentCanvasPanel(space_mode, dp_input_mode);
    panel->canvas_size_factor_saved_ = size_factor;
    HERON_LOG_DEBUG("{} space:{}, dp_in:{} size_factor:{}", __FUNCTION__, space_mode, dp_input_mode, panel->canvas_size_factor_saved_);
}
