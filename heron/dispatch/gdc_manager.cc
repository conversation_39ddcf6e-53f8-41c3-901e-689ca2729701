#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/model/permanent_config.h>
#include <heron/model/glasses_config.h>
#include <heron/util/math_tools.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <fstream>

using namespace heron::dispatch;
using namespace heron::model;
using namespace heron;

/****************************************
 *
 *		GDC
 *
 * ****************************************/
// XXX: must be called after lifecycle initialize. For now, it is called in lifecycle.start()
GDC::GDC(GDC_USAGE usage) : usage_(usage)
{
    HERON_LOG_DEBUG("GDC {} constructor", usage_ == GDC_USAGE_LEFT_DISPLAY ? "left" : "right");
    output_height_ = ModelManager::GetInstance()->display_metadatas_[usage_].height;
    output_width_ = ModelManager::GetInstance()->display_metadatas_[usage_].width;
    mesh_num_rows_ = ModelManager::GetInstance()->display_metadatas_[usage_].distortion_info.num_rows;
    mesh_num_columns_ = ModelManager::GetInstance()->display_metadatas_[usage_].distortion_info.num_columns;
    if (mesh_num_rows_ != DIV_CEIL(output_height_, ROW_COUNT_IN_BLOCK) + 1)
    {
        HERON_LOG_ERROR("mesh_row_count mismatch expect: {} got: {}", DIV_CEIL(output_height_, ROW_COUNT_IN_BLOCK) + 1, mesh_num_rows_);
        usleep(500 * 1000);
        std::abort();
    }
    if (mesh_num_columns_ != DIV_CEIL(output_width_, COLUMN_COUNT_IN_BLOCK) + 1)
    {
        HERON_LOG_ERROR("mesh_column_count mismatch  expect: {} got: {}", DIV_CEIL(output_width_, COLUMN_COUNT_IN_BLOCK) + 1, mesh_num_columns_);
        usleep(500 * 1000);
        std::abort();
    }

    status_ = std::make_unique<GdcInitConfig>();
    /**
    * @brief  在用户态分配MMZ内存.
    * @param  u64_phy_addr 分配的物理地址指针.
    * @param  p_vir_addr 指向分配的虚拟地址指针的指针.
    * @param  pstr_mmb Mmb 名称的字符串指针.
    * @param  pstr_zone MMZ zone 名称的字符串指针.
    * @param  u32_len 内存块大小.
    * @retval 0 成功 , 其它 失败.
    * @note   MMZ分为许多区域（Zone），每个区域下有多个Mmb，调用此接口在MMZ的名为
              *pstr_zone 的区域中分配一个名为*pstr_mmb 的内存块，大小为u32_len，并返回物理地
              址和用户态虚拟地址指针。如果MMZ中有名为anonymous的区域，*pstr_zone可设为
              NULL。如果*pstr_mmb 设为NULL，创建的Mmb 分块名为“<null>”
    */
    char name[16] = {};
    const char *zone_name = DebugManager::GetInstance()->gdc_configs[usage_].use_sram ? "sram" : nullptr;
    snprintf(name, sizeof(name), "warp%d", usage_);
    uint32_t matrix_buffer_size = mesh_num_columns_ * mesh_num_rows_ * MATRIX_ROW * MATRIX_COLUMN / DebugManager::GetInstance()->gdc_configs[usage_].warp_flush_cnt * sizeof(float);
    if (DebugManager::GetInstance()->use_mmz_alloc_cached)
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAllocCached(
                &matrices_physical_address_,
                &matrices_virtual_address_,
                name,
                zone_name,
                matrix_buffer_size))
        {
            HERON_LOG_ERROR("ARMmzAllocCached for {} failed.", name);
        }
    }
    else
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
                &matrices_physical_address_,
                &matrices_virtual_address_,
                name,
                zone_name,
                matrix_buffer_size))
        {
            HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
        }
    }
    for (uint32_t row = 0; row < mesh_num_rows_; row++)
    {
        for (uint32_t col = 0; col < mesh_num_columns_; col++)
            memcpy((char *)matrices_virtual_address_ + (row * mesh_num_columns_ + col) * 36,
                   (void *)(DebugManager::GetInstance()->debug_warp_matrices.data() + row * 36), 36);
    }
    if (DebugManager::GetInstance()->use_mmz_alloc_cached)
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzFlushCache(matrices_physical_address_,
                                                               matrices_virtual_address_, matrix_buffer_size))
        {
            HERON_LOG_ERROR("ARMmzFlushCache for {} failed.", name);
        }
    }
    snprintf(name, sizeof(name), "mesh%d", usage_);
    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
            &mesh_physical_address_,
            &mesh_virtual_address_,
            name,
            zone_name,
            mesh_num_rows_ * mesh_num_columns_ * MESH_VERTEX_DATA_SIZE * sizeof(float)))
    {
        HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
    }
    memcpy(mesh_virtual_address_,
           ModelManager::GetInstance()->display_metadatas_[usage_].distortion_info.mesh_for_gdc.data(),
           mesh_num_rows_ * mesh_num_columns_ * MESH_VERTEX_DATA_SIZE * sizeof(float));
    snprintf(name, sizeof(name), "identity_mesh%d", usage_);
    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
            &identity_mesh_physical_address_,
            &identity_mesh_virtual_address_,
            name,
            nullptr,
            mesh_num_rows_ * mesh_num_columns_ * MESH_VERTEX_DATA_SIZE * sizeof(float)))
    {
        HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
    }
    memcpy(identity_mesh_virtual_address_,
           ModelManager::GetInstance()->display_metadatas_[usage_].distortion_info.identity_mesh_for_gdc.data(),
           mesh_num_rows_ * mesh_num_columns_ * MESH_VERTEX_DATA_SIZE * sizeof(float));

    snprintf(name, sizeof(name), "weight%d", usage_);
    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
            &weight_physical_address_,
            &weight_virtual_address_,
            name,
            zone_name,
            ROW_COUNT_IN_BLOCK * COLUMN_COUNT_IN_BLOCK * 4 * sizeof(float)))
    {
        HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
    }
    std::vector<float> weights;
    GenGDCWeight(COLUMN_COUNT_IN_BLOCK, ROW_COUNT_IN_BLOCK, (float *)weight_virtual_address_);
    InitStatus();
}

#define MMZ_FREE(PHYSICAL, VIRTUAL)                          \
    if (PHYSICAL != 0 && VIRTUAL != nullptr)                 \
    {                                                        \
        if (!DispatcherWrapper::GetInstance()->ARMmzDealloc( \
                PHYSICAL, VIRTUAL))                          \
        {                                                    \
            HERON_LOG_ERROR("ARMmzDealloc failed");          \
        }                                                    \
    }

GDC::~GDC()
{
    MMZ_FREE(matrices_physical_address_, matrices_virtual_address_);
    MMZ_FREE(mesh_physical_address_, mesh_virtual_address_);
    MMZ_FREE(weight_physical_address_, weight_virtual_address_);
}

Result GDC::RenderFrame(const DpFrameData &src_frame, const SpaceScreenStatus &status, bool need_reset, uint8_t frame_start)
{
    DpFrameData current_frame = src_frame;
    if (status.dp_input_mode == DP_INPUT_MODE_STEREO)
    {
        current_frame.width /= 2;
        if (usage_ == GDC_USAGE_RIGHT_DISPLAY)
        {
            current_frame.data[0] += current_frame.width;
            current_frame.data[1] += (current_frame.width / 2);
            current_frame.data[2] += (current_frame.width / 2);
            current_frame.data_ext[0] += current_frame.width;
            current_frame.data_ext[1] += (current_frame.width / 2);
            current_frame.data_ext[2] += (current_frame.width / 2);
        }
    }

    DebugManager *p_dm = DebugManager::GetInstance();
    GdcFrameConfig gdc_frame_config;
    if ((p_dm->disable_warp_at_0DOF && status.perception_type == PERCEPTION_TYPE_0DOF) || status.lsr_mode == WARP_MODE_NONE)
    {
        gdc_frame_config.metadata.mesh_data_data = (const char *)identity_mesh_physical_address_;
        gdc_frame_config.warp_mode = 2; // disable
    }
    else
    {
        gdc_frame_config.metadata.mesh_data_data = (const char *)mesh_physical_address_;
        gdc_frame_config.warp_mode = p_dm->gdc_configs[usage_].warp_mode;
    }
    if (status.validation.NotToPresent())
    {
        // use looking at back apb matrix
        gdc_frame_config.warp_mode = 1;              // apb matrix;
        gdc_frame_config.apb_matrix[0] = 0x3f800000; // 1.0
        gdc_frame_config.apb_matrix[1] = 0;
        gdc_frame_config.apb_matrix[2] = 0;
        gdc_frame_config.apb_matrix[3] = 0;
        gdc_frame_config.apb_matrix[4] = 0x3f800000; // 1.0
        gdc_frame_config.apb_matrix[5] = 0;
        gdc_frame_config.apb_matrix[6] = 0;
        gdc_frame_config.apb_matrix[7] = 0;
        gdc_frame_config.apb_matrix[8] = 0xC0400000; // -1.0
    }
    gdc_frame_config.padding_color[0] = p_dm->gdc_configs[usage_].padding_value_c0; // GDC采样超出原图区域的颜色(通道1)
    gdc_frame_config.padding_color[1] = p_dm->gdc_configs[usage_].padding_value_c1; // GDC采样超出原图区域的颜色(通道2)
    gdc_frame_config.padding_color[2] = p_dm->gdc_configs[usage_].padding_value_c2; // GDC采样超出原图区域的颜色(通道3)
    gdc_frame_config.frame_start = frame_start;
    gdc_frame_config.display_usage = (DisplayUsage)usage_;
    gdc_frame_config.in_buffer.is_vb = 0;                     // XXX
    gdc_frame_config.in_buffer.width = current_frame.width;   // GDC输入图像width
    gdc_frame_config.in_buffer.height = current_frame.height; // GDC输入图像height
    // VI获取的Frame每个Chanel的stride,直接赋值透传给GDC的相应寄存器即可
    gdc_frame_config.in_buffer.strides[0] = current_frame.strides[0]; // 图像第一个通道的stride,目前YUV420的源,此值是2048
    gdc_frame_config.in_buffer.strides[1] = current_frame.strides[1]; // 图像第二个通道的stride,目前YUV420的源,此值是1024
    gdc_frame_config.in_buffer.strides[2] = current_frame.strides[2]; // 图像第三个通道的stride,目前YUV420的源,此值是1024

    // VI获取的Frame每个Chanel的物理地址,直接赋值透传给GDC的相应寄存器即可
    gdc_frame_config.in_buffer.data_ext[0] = (uint32_t)((uint64_t)current_frame.data_ext[0] & 0xffffffff); // 图像第一个通道的物理地址
    gdc_frame_config.in_buffer.data_ext[1] = (uint32_t)((uint64_t)current_frame.data_ext[1] & 0xffffffff); // 图像第二个通道的物理地址
    gdc_frame_config.in_buffer.data_ext[2] = (uint32_t)((uint64_t)current_frame.data_ext[2] & 0xffffffff); // 图像第三个通道的物理地址

    if (!DispatcherWrapper::GetInstance()->GdcProcess(gdc_frame_config, need_reset))
    {
        HERON_LOG_ERROR("gdc:{} process error", usage_ == GDC_USAGE_LEFT_DISPLAY ? "left" : "right");
        return Result::RESULT_FAILURE;
    }
    return Result::RESULT_SUCCESS;
}

void GDC::InitStatus()
{
    // GDC status initialization
    memset(status_.get(), 0, sizeof(GdcInitConfig));

    status_->display_usage = (DisplayUsage)usage_; // 范围为{0,1,2},写死，对应板子上三个GDC硬件资源
    /**
     *0:normal模式
     *cpu向gdc发送frame_start一次，gdc就只启动一次，完成一帧计算。
     *
     *1:auto 模式
     *cpu只需向gdc发送frame_start一次，之后gdc将会在接收到in_bp_vsyn (由前级直接送入gdc的硬连线信号) 后自动start，进行后一帧的计算。
     * (需in_bp_enable、in_bp_lowdelay_prot_en有效)
     *
     *2:shadow 模式(目前验证过的模式，配合cpu_bp_enable=true使用)
     *gdc内部有两组寄存器：current/shadow_group，cpu可以在发出一次frame_start后，立即进行下一帧的配置并发出 frame_start(AR_MPI_GDC_ADV_Start)。
     *在此模式下 frame_start 可以看作是两组寄存器的有效标记，gdc计算完当前帧(shadow寄存器使用完毕)后，会根据标记有效性，切换寄存器组并开始下一帧计算。
     *(当前gdc中处于等待状态的有效寄存器组可以通过查询config_groups获取，理论上当前gdc中有两组配置未被执行时不应该配入第三组，不然第二组寄存器会被覆盖;
     *cpu不需使用不同地址区分current/shadow_group，gdc会根据start信号自动将寄存器存入对应寄存器组)
     *
     *3:free 模式
     *cpu只需发送frame _start一次，之后gdc会在前帧计算结束frame_done时自动start然后开始计算下一帧。
     *此模式可理解为gdc在以自己最大能力处理图像，此模式下gdc会自动关闭in/cpu_bp功能，但display仍然可以通过line_buffer间接反压gdc。
     *此模式主要用于gdc与display深度捆绑的场景，gdc可看成只是在为display读取数据。
     *
     */
    status_->start_mode = DebugManager::GetInstance()->gdc_configs[usage_].start_mode;
    if (DebugManager::GetInstance()->lines_64_enable)
        status_->lines64_enable = 1;                                                               // XXXX
    status_->padding_color[0] = DebugManager::GetInstance()->gdc_configs[usage_].padding_value_c0; // GDC采样超出原图区域的颜色(通道1)
    status_->padding_color[1] = DebugManager::GetInstance()->gdc_configs[usage_].padding_value_c1; // GDC采样超出原图区域的颜色(通道2)
    status_->padding_color[2] = DebugManager::GetInstance()->gdc_configs[usage_].padding_value_c2; // GDC采样超出原图区域的颜色(通道3)

    // 0: use warp.dat file; 1 use apb matrix; 2 disable。我们用0
    status_->warp_mode = DebugManager::GetInstance()->gdc_configs[usage_].warp_mode;
    // 每个3x3warp矩阵复用几次。每行用同一个矩阵则矩阵一帧共35个矩阵，warp_flush_cnt = 61
    status_->warp_flush_cnt = 1;
    // 0:32x32 blocking; 3:disable。我们用0
    status_->mesh_mode = DebugManager::GetInstance()->gdc_configs[usage_].mesh_mode;
    // mesh每行1920/32=61个点,每个点两个float,61x8bytes=488
    status_->mesh_stride = mesh_num_columns_ * MESH_VERTEX_DATA_SIZE * sizeof(float);
    // 0 inner bi-linear;1 use external file; 我们用1
    status_->weight_mode = DebugManager::GetInstance()->gdc_configs[usage_].weight_mode;
    // warp矩阵list的MMZ起始地址
    status_->metadata.warp_data_data = (const char *)matrices_physical_address_;
    status_->metadata.mesh_data_data = (const char *)mesh_physical_address_;
    status_->metadata.weight_data_data = (const char *)weight_physical_address_;
    DispatcherWrapper::GetInstance()->GdcInit(*status_.get());
    // status_->stGdcParam.stAxiCfg.u8AxiQos = 15; // test if it can avoid underflow
    HERON_LOG_DEBUG("GDC {} InitStatus done.", usage_ == GDC_USAGE_LEFT_DISPLAY ? "left" : "right");
}

/****************************************
 *
 *		GDCManager
 *
 * ****************************************/
GDCManager::GDCManager()
{
    gdc_map_.clear();
}

GDCManager::~GDCManager()
{
    MMZ_FREE(dummy_frame_physical_address_, dummy_frame_virtual_address_);
    MMZ_FREE(blank_frame_physical_address_, blank_frame_virtual_address_);
}

void GDCManager::Init()
{
    gdc_map_[GDC_USAGE_LEFT_DISPLAY] = std::make_unique<GDC>(GDC_USAGE_LEFT_DISPLAY);
    gdc_map_[GDC_USAGE_RIGHT_DISPLAY] = std::make_unique<GDC>(GDC_USAGE_RIGHT_DISPLAY);
    PopulateDummyDpFrame(DP_DUMMY_FRAME_WIDTH, DP_DUMMY_FRAME_HEIGHT,
                         {(uint8_t)DebugManager::GetInstance()->gdc_configs[0].padding_value_c0,
                          (uint8_t)DebugManager::GetInstance()->gdc_configs[0].padding_value_c1,
                          (uint8_t)DebugManager::GetInstance()->gdc_configs[0].padding_value_c2},
                         dummy_frame_, &dummy_frame_virtual_address_, &dummy_frame_physical_address_, "PreImg");
    PopulateDummyDpFrame(DP_DUMMY_FRAME_WIDTH, DP_DUMMY_FRAME_HEIGHT, PermanentConfig::GetInstance()->GetBlankFrameColor(),
                         blank_frame_, &blank_frame_virtual_address_, &blank_frame_physical_address_, "BlankImg");
}

void GDCManager::PopulateDummyDpFrame(uint32_t width, uint32_t height, const std::vector<uint8_t> &color, DpFrameData &dp_frame_data,
                                      void **virtual_address, uint64_t *physical_address, const char *name)
{
    uint32_t dummy_frame_y_size = ALIGN128(width);
    uint32_t dummy_frame_uv_size = ALIGN128(width / 2);
    uint32_t dummy_frame_width = width;
    uint32_t dummy_frame_height = height;
    uint32_t size = dummy_frame_y_size + dummy_frame_uv_size * 2;
    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
            physical_address, virtual_address, name, nullptr, size))
    {
        HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
        return;
    }
    HERON_LOG_DEBUG("{} memset: {},{},{} of size: {}", name, color[0], color[1], color[2], size);
    // get frame
    dp_frame_data.ar_frame_handle = 0;
    dp_frame_data.data[0] = (char *)*virtual_address;
    memset((void *)dp_frame_data.data[0], (char)color[0], dummy_frame_y_size);
    dp_frame_data.data[1] = dp_frame_data.data[0] + dummy_frame_y_size;
    memset((void *)dp_frame_data.data[1], (char)color[1], dummy_frame_uv_size);
    dp_frame_data.data[2] = dp_frame_data.data[1] + dummy_frame_uv_size;
    memset((void *)dp_frame_data.data[2], (char)color[2], dummy_frame_uv_size);
    dp_frame_data.data_ext[0] = (char *)*physical_address;
    dp_frame_data.data_ext[1] = dp_frame_data.data_ext[0] + dummy_frame_y_size;
    dp_frame_data.data_ext[2] = dp_frame_data.data_ext[1] + dummy_frame_uv_size;
    dp_frame_data.frame_id = 0;
    dp_frame_data.width = dummy_frame_width;
    dp_frame_data.height = dummy_frame_height;
    dp_frame_data.strides[0] = 0;
    dp_frame_data.strides[1] = 0;
    dp_frame_data.strides[2] = 0;
    dp_frame_data.pts = 0;
    dp_frame_data.pixel_format = FRAMEBUFFER_FORMAT_YUV420_PLANAR;
}