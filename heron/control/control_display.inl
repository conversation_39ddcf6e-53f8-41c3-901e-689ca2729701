#include <heron/model/model_manager.h>
#include <heron/interface_provider/flinger.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <heron/env/export.h>

using namespace heron;
using namespace control;
using namespace model;
using namespace interface_provider;

HERON_FORCE_INLINE void DisplayCtrl::UpdateHeadTransform(Transform &head_transform, uint32_t source_interrupt_id, DisplayUsage display_usage,
                                                         uint32_t target_block_id, uint64_t timestamp_nanos)
{
    ModelManager *p_mm = ModelManager::GetInstance();
    // HERON_LOG_TRACE("source_interrupt_id: {}, target_block_id: {}", source_interrupt_id, target_block_id);
    //  assume that target_block_id will not larger than num_rows*2
    if (target_block_id >= p_mm->display_metadatas_[DISPLAY_USAGE_LEFT].distortion_info.num_rows)
        timestamp_nanos += (p_mm->GetDisplayVBlank() + p_mm->GetDisplayVAfter());

    // pose_latency_[display_usage].Update(timestamp_nanos - GetTimeNano());
    // XXX: returns looking at direction (0,0,1) which means look stright back when real interface api returns error to reduce GDC memory access
    bool get_head_pose_result = true;
    DebugManager *p_dm = DebugManager::GetInstance();
    timestamp_nanos += p_dm->pose_timestamp_offset_ns;
    if (!space_screen_status_in_use_[display_usage]->pupil_adjust)
    {
        // head_transform is identity defaultly
        get_head_pose_result = FlingerInterface::GetInstance()->GetDevicePose(
            space_screen_status_in_use_[display_usage]->perception_type,
            timestamp_nanos, TRACKING_POSE_TYPE_LOW_LATENCY, &head_transform);
        get_headpose_count_++;
        if (p_dm->debug_log.head_pose && get_headpose_count_ % p_dm->print_interrupt_interval == 0)
            PrintObject("head_pose", head_transform);
        if (!get_head_pose_result)
            get_headpose_error_count_++;
    }
    if (display_usage != DISPLAY_USAGE_LEFT)
        return;
    if (source_interrupt_id != 0)
    {
        should_assign_latest_head_transform_ = true;
        return;
    }
    if (!should_assign_latest_head_transform_)
        return;
    should_assign_latest_head_transform_ = false;
    latest_head_transform_ = head_transform;
    latest_head_transform_valid_ = get_head_pose_result;
}

HERON_FORCE_INLINE void DisplayCtrl::ApplyNextFrameStatus(DisplayUsage display_usage, uint32_t get_frame_and_config_gdc_at)
{
    DebugManager *p_dm = DebugManager::GetInstance();
    ModelManager *p_mm = ModelManager::GetInstance();
    *space_screen_status_in_use_[display_usage] = latest_frame_info_got_->space_screen_status;
    Vector2i quad_size_pixel(latest_frame_info_got_->dp_frame_data.width, latest_frame_info_got_->dp_frame_data.height);
    if (space_screen_status_in_use_[display_usage]->dp_input_mode == DP_INPUT_MODE_STEREO)
        quad_size_pixel.x() /= 2;
    get_frame_and_config_gdc_at_ = get_frame_and_config_gdc_at;
    Transform quad_transform;
    space_screen_status_in_use_[display_usage]->GetTransform(quad_transform);
    Vector2f size_meters;
    space_screen_status_in_use_[display_usage]->GetCurrentSizeMeters(size_meters);
    switch (space_screen_status_in_use_[display_usage]->scene_mode)
    {
    case SCENE_MODE_WITH_NEBULA:
        switch (latest_frame_info_got_->metadata.lsr_mode)
        {
        case WARP_MODE_NONE:
            break;
        case WARP_MODE_PLANE:
            if (latest_frame_info_got_->metadata.metadata_vec.size() != 2)
            {
                HERON_LOG_WARN("buffer_metadata_size error {} WARP_MODE_PLANE", latest_frame_info_got_->metadata.metadata_vec.size());
            }
            else
            {
                p_mm->GetHostWarp(display_usage)->SetFromTransform(latest_frame_info_got_->metadata.metadata_vec[display_usage].pose);
                p_mm->GetHostWarp(display_usage)->SetPlaneInfo(latest_frame_info_got_->metadata.metadata_vec[display_usage].plane_point, latest_frame_info_got_->metadata.metadata_vec[display_usage].plane_normal, false);
                p_mm->GetHostWarp(display_usage)->SetDisplayProjectMatrix(p_mm->host_modified_display_projection_[display_usage]);
                p_mm->GetHostWarp(display_usage)->SetHostProjectMatrix(space_screen_status_in_use_[display_usage]->host_projection[display_usage]);
            }
            break;
        case WARP_MODE_DEPTH:
            if (latest_frame_info_got_->metadata.metadata_vec.size() != 2)
            {
                HERON_LOG_WARN("buffer_metadata_size error {} WARP_MODE_DEPTH", latest_frame_info_got_->metadata.metadata_vec.size());
            }
            else
            {
                if (p_dm->use_host_ptw_warp)
                {
                    p_mm->GetHostPtwWarp(display_usage)->SetFromTransform(latest_frame_info_got_->metadata.metadata_vec[display_usage].pose);
                    p_mm->GetHostPtwWarp(display_usage)->SetDisplayProjectMatrix(p_mm->host_modified_display_projection_[display_usage]);
                    p_mm->GetHostPtwWarp(display_usage)->SetHostProjectMatrix(space_screen_status_in_use_[display_usage]->host_projection[display_usage]);
                }
                else
                {
                    HERON_LOG_WARN("unsupported warp_mode: {} for SCENE_MODE_WITH_NEBULA", latest_frame_info_got_->metadata.lsr_mode);
                }
            }
            break;
        default:
            HERON_LOG_WARN("unsupported warp_mode: {} for SCENE_MODE_WITH_NEBULA", latest_frame_info_got_->metadata.lsr_mode);
        }

        break;
    case SCENE_MODE_SPACE_SCREEN:
        recentered_quad_transform_[display_usage] = quad_transform;
        if (!space_screen_status_in_use_[display_usage]->pupil_adjust)
        {
            recentered_quad_transform_[display_usage].position = recenter_transform_.rotation * quad_transform.position + recenter_transform_.position;
            recentered_quad_transform_[display_usage].rotation = recenter_transform_.rotation * quad_transform.rotation;
        }
        if (space_screen_status_in_use_[display_usage]->space_mode == SPACE_MODE_ULTRA_WIDE)
        {
            p_mm->GetCylinderWarp(display_usage)->SetQuadSizePixel(quad_size_pixel);
            p_mm->GetCylinderWarp(display_usage)->SetQuadSizeMeters(size_meters);
            p_mm->GetCylinderWarp(display_usage)->SetQuadTransform(recentered_quad_transform_[display_usage]);
            p_mm->GetCylinderWarp(display_usage)->SetQuadRadius(-quad_transform.position.z());
        }
        else
        {
            p_mm->GetGDCWarp(display_usage)->SetQuadSizePixel(quad_size_pixel);
            p_mm->GetGDCWarp(display_usage)->SetQuadSizeMeters(size_meters);
            p_mm->GetGDCWarp(display_usage)->SetQuadTransform(recentered_quad_transform_[display_usage]);
        }
        break;
    default:
        HERON_LOG_ERROR("invalid scene mode on ApplyNextFrameStatus");
    }
    if (vsync_callback_count_[display_usage] % p_dm->print_frame_interval == 0 && display_usage == DISPLAY_USAGE_LEFT)
        PrintStatusInfo(size_meters, quad_transform.position);
}
