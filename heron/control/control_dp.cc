#include <heron/control/control_dp.h>
#include <heron/control/control_display.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/dp_manager.h>
#include <heron/interface_provider/flinger.h>
#include <heron/message/m_type_converter.h>
#include <heron/model/model_manager.h>
#include <heron/util/math_tools.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

using namespace heron;
using namespace model;
using namespace control;
using namespace dispatch;
using namespace interface_provider;

void DpCtrl::Start()
{
    DpManager::GetInstance()->StartReceiveThread();
}

void DpCtrl::Stop()
{
    // stop dp
    HERON_LOG_INFO("DisplayCtrl Dp Stopping...");
    DpManager::GetInstance()->StopReceiveThread();
    HERON_LOG_INFO("DisplayCtrl Dp Stopped.");
}

void DpCtrl::StartPresentDpFrames(const ResolutionInfo &res_info)
{
    dp_render_stop_ = false;
    ModelManager::GetInstance()->UpdateCurrentDpSrcSizePixel(Vector2i(res_info.width, res_info.height));
    DpManager::GetInstance()->SetExpectedFps((float)res_info.refresh_rate);
}

static void Flush(RingBuffer<FrameInfo> *frame_infos)
{
    for (int32_t i = 0; i < frame_infos->GetSize(); ++i)
    {
        FrameInfo *frame_info = nullptr;
        if (frame_infos->IsIndexValid(i))
            frame_info = frame_infos->GetBuffer(i);
        if (!frame_info)
        {
            HERON_LOG_WARN("GetBufferByIdx: {} error on flush.", i);
            return;
        }
        frame_info->space_screen_status.validation.flushed_src_frame = true;
    }
}

void DpCtrl::StopPresentDpFrames()
{
    dp_render_stop_ = true;
    DpManager::GetInstance()->ResetConsecutiveValidFrameCount();
    RingBuffer<FrameInfo> *frame_infos =
        ModelManager::GetInstance()->GetFrameInfos(FRAME_USAGE_ANY);
    Flush(frame_infos);
}

static uint32_t s_last_frame_id = 0;
static uint32_t s_frame_saved_count = 0;
static bool s_last_frame_view_good = false;
bool DpCtrl::ParseFrame(FrameInfo &frame_info)
{
    parse_frame_count_++;
    //  TODO: parse frame to get usage any/left/right
    frame_info.usage = FrameUsage::FRAME_USAGE_ANY;
    if (frame_info.dp_frame_data.frame_id - s_last_frame_id != 1)
    {
        HERON_LOG_WARN("Detect src frame inconsistency. frame_id: {} last_frame_id: {}", frame_info.dp_frame_data.frame_id, s_last_frame_id);
    }
    s_last_frame_id = frame_info.dp_frame_data.frame_id;
    frame_info.space_screen_status = *ModelManager::GetInstance()->GetSpaceScreenStatus();
    frame_info.space_screen_status.validation.flushed_src_frame = dp_render_stop_;
    if (dp_render_stop_)
        return false;
    if (curr_frame_scene_mode_ == SCENE_MODE_WITH_NEBULA)
        return true;
    bool good_view = true;
    Vector4f tl, tr, bl, br;
    bool h_factor_valid = false;
    bool v_factor_valid = false;
    good_view = DisplayCtrl::GetInstance()->GetCanvasCornerHomogeneous(tl, tr, bl, br);
    Vector2i screen_size_pixel = ModelManager::GetInstance()->GetScreenSizePixel();
    if (good_view)
        good_view &= GetCanvasProjectionSizeFactor(tl, tr, bl, br, screen_size_pixel, frame_info.target_size_factor, h_factor_valid, v_factor_valid); // factors always valid for now
    if (good_view && frame_info.space_screen_status.space_mode == SPACE_MODE_ULTRA_WIDE)
    {
        bool inner, outer;
        DisplayCtrl::GetInstance()->InCylinder(inner, outer);
        if (!outer)
            good_view = false;
        else if (!inner)
            good_view = s_last_frame_view_good;
    }
    frame_info.space_screen_status.validation.bad_view = !good_view;
    s_last_frame_view_good = good_view;
    return true;
}

static vector<float> target_size_factors{64 / 1920.0f, 128 / 1920.0f, 320 / 1920.0f, 480 / 1920.0f, 576 / 1920.0f, 768 / 1920.0f, 960 / 1920.0f, 1280 / 1920.0f, 1600 / 1920.0f};
static uint32_t s_unexpected_resolution_frame_count = 0;
static bool s_error_printed = false;
void DpCtrl::UpdateSuitableSrcFrameSize(uint32_t curr_width, uint32_t curr_height, const SpaceScreenStatus &space_screen_status, const Vector2f &target_size_factor)
{
    ModelManager *p_mm = ModelManager::GetInstance();
    if (p_mm->GetCurrentDpSrcSizePixel().x() != p_mm->GetDefaultDpSrcSizePixel().x())
        return;
    if (space_screen_status.scene_mode == SCENE_MODE_WITH_NEBULA || space_screen_status.space_mode == SPACE_MODE_ULTRA_WIDE)
    {
        s_unexpected_resolution_frame_count++;
        if (s_unexpected_resolution_frame_count % DebugManager::GetInstance()->print_frame_interval == 0 || !s_error_printed)
        {
            s_error_printed = true;
            HERON_LOG_WARN("unexpected resolution frame count:{}", s_unexpected_resolution_frame_count);
        }
        return; // should not reach this return;
    }
    s_error_printed = false;
    Vector2i screen_size_pixel = p_mm->GetScreenSizePixel();
    uint32_t target_width = screen_size_pixel.x();
    uint32_t target_height = screen_size_pixel.y();
    DebugManager *p_dm = DebugManager::GetInstance();
    if (!p_dm->disable_warp_at_0DOF || space_screen_status.perception_type != PERCEPTION_TYPE_0DOF)
    {
        // Ensure target_width and target_height are multiples of 4
        if (auto it = std::upper_bound(target_size_factors.begin(), target_size_factors.end(), target_size_factor.x()); it != target_size_factors.end())
            target_width = ALIGN_BACK((uint32_t)((*it) * screen_size_pixel.x()), 4);
        if (auto it = std::upper_bound(target_size_factors.begin(), target_size_factors.end(), target_size_factor.y()); it != target_size_factors.end())
            target_height = ALIGN_BACK((uint32_t)((*it) * screen_size_pixel.y()), 4);
    }
    if (p_dm->arbitrary_src_size_pixel)
    {
        target_width = p_dm->target_src_size_pixel.x();
        target_height = p_dm->target_src_size_pixel.y();
    }
    if (curr_height != target_height || curr_width != target_width)
    {
        if (p_dm->debug_log.suitable_src_frame_size)
        {
            HERON_LOG_DEBUG("curr_src_size:{}x{} need resize to {}x{} forcely_set:{}",
                            curr_width, curr_height, target_width, target_height, p_dm->arbitrary_src_size_pixel);
        }
        DispatcherWrapper::GetInstance()->DpResizeFrame(target_width, target_height);
    }
}

bool DpCtrl::ParseEmbeddedData(FrameInfo &frame_info, bool &embedded_simple,
                               FrameEmbeddedInfoSimpleTwin &embedded_info_simple, FrameEmbeddedInfoTwin &embedded_info)
{
    std::vector<char> decodded;
    uint32_t debug_out_data_size = 0;
    if (!FlingerInterface::GetInstance()->BWDecodeBuffer(IMAGE_FORMAT_GRAY_8, frame_info.dp_frame_data.width, (const char *)frame_info.dp_frame_data.data[0], &debug_out_data_size, nullptr))
        return false;
    if (debug_out_data_size == 0)
        return false;
    decodded.resize(debug_out_data_size);
    if (!FlingerInterface::GetInstance()->BWDecodeBuffer(IMAGE_FORMAT_GRAY_8, frame_info.dp_frame_data.width, (const char *)frame_info.dp_frame_data.data[0], &debug_out_data_size, decodded.data()))
        return false;
    FrameMetaDataHeader *header = (FrameMetaDataHeader *)(decodded.data());
    if (header->magic_number_high == 0x5AF48B7B)
    {
        msg::FrameEmbeddedInfo embedded_info_pb;
        if (!embedded_info_pb.DeserializePB((const void *)(decodded.data() + sizeof(FrameMetaDataHeader)), decodded.size() - sizeof(FrameMetaDataHeader)))
        {
            HERON_LOG_WARN("Failed to deserialize normal embedded info.")
            return false;
        }
        FrameEmbeddedInfoToFrameEmbeddedInfoTwin(embedded_info, embedded_info_pb);
        embedded_simple = false;
    }
    else if (header->magic_number_high == 0x7B630EBB)
    {
        msg::FrameEmbeddedInfoSimple embedded_info_simple_pb;
        if (!embedded_info_simple_pb.DeserializePB((const void *)(decodded.data() + sizeof(FrameMetaDataHeader)), decodded.size() - sizeof(FrameMetaDataHeader)))
        {
            HERON_LOG_WARN("Failed to deserialize simple embedded info.")
            return false;
        }
        FrameEmbeddedInfoSimpleToFrameEmbeddedInfoSimpleTwin(embedded_info_simple, embedded_info_simple_pb);
        embedded_simple = true;
    }
    else
    {
        HERON_LOG_WARN("Wrong magic number.")
        return false;
    }
    if (DebugManager::GetInstance()->hide_metadata_lines)
    {
        frame_info.dp_frame_data.data_ext[0] += (frame_info.dp_frame_data.strides[0] * 2); // hide embedded metadata lines
        frame_info.dp_frame_data.data_ext[1] += frame_info.dp_frame_data.strides[1];       // hide embedded metadata lines
        frame_info.dp_frame_data.data_ext[2] += frame_info.dp_frame_data.strides[2];       // hide embedded metadata lines
        frame_info.dp_frame_data.height -= 2;                                              // hide embedded metadata lines
    }
    if (!DebugManager::GetInstance()->debug_log.embedded_metadata)
        return true;
    if (parse_frame_count_ % DebugManager::GetInstance()->print_frame_interval != 0)
        return true;
    HERON_LOG_DEBUG("decodded HEX size: {}", decodded.size());
    HERON_LOG_HEX("decodded HEX: {}", std::string(decodded.begin(), decodded.end()));
    HERON_LOG_DEBUG("embedded data header high:{}  low:{} len:{} simple:{}",
                    header->magic_number_high, header->magic_number_low, header->len, embedded_simple);
    return true;
}

static Average s_msg_recv_till_dp_rx_done("msg_recv_till_dp_rx_done");
static Average s_dp_transfer_time("dp_transfer");
static Average s_render_to_predict("render_to_predict");
static Average s_render_to_compose("render_to_compose");
static Average s_compose_to_receive("compose_to_receive");
bool DpCtrl::PopulateFrameInfoMetadata(FrameInfo &frame_info, bool embedded_simple,
                                       const FrameEmbeddedInfoSimpleTwin &embedded_info_simple,
                                       const FrameEmbeddedInfoTwin &embedded_info)
{
    DebugManager *p_dm = DebugManager::GetInstance();
    if (embedded_simple)
    {
        FrameMetaInfoTwin meta_info;
        uint64_t metadata_recv_ns;
        if (!ModelManager::GetInstance()->GetFrameMetaInfo(embedded_info_simple.frame_number, metadata_recv_ns, meta_info))
        {
            HERON_LOG_WARN("Failed to get metadata for frame number: {}", embedded_info_simple.frame_number);
            return false;
        }
        FrameMetaInfoTwinToFrameMetadataInternal(frame_info.metadata, meta_info);
        frame_info.metadata.timing.embedded_device_ns = embedded_info_simple.embedded_device_time;
        frame_info.metadata.timing.data_recv_ns = metadata_recv_ns;
        frame_info.space_screen_status.dp_input_mode = (DpInputMode)meta_info.framebuffer_mode;
        if (p_dm->profile_atw || p_dm->debug_log.timing_detail)
        {
            s_msg_recv_till_dp_rx_done.Update(frame_info.metadata.timing.dp_rx_done_ns - metadata_recv_ns);
            s_dp_transfer_time.Update(frame_info.metadata.timing.dp_rx_done_ns - embedded_info_simple.embedded_device_time);
        }
    }
    else
    {
        FrameEmbeddedInfoTwinToFrameMetadataInternal(frame_info.metadata, embedded_info);
        frame_info.space_screen_status.dp_input_mode = (DpInputMode)embedded_info.framebuffer_mode;
    }
    frame_info.space_screen_status.lsr_mode = frame_info.metadata.lsr_mode;
    switch (frame_info.metadata.lsr_mode)
    {
    case WARP_MODE_PLANE:
        if (frame_info.metadata.metadata_vec.size() != 2)
        {
            HERON_LOG_WARN("unsupported metadata_vec size: {} for WARP_MODE_PLANE", frame_info.metadata.metadata_vec.size());
            return false;
        }
        for (uint32_t i = 0; i < 2; i++)
        {
            ModelManager::GetInstance()->UpdateHostProjection(
                frame_info.metadata.metadata_vec[i].fov.left_tan,
                frame_info.metadata.metadata_vec[i].fov.right_tan,
                frame_info.metadata.metadata_vec[i].fov.top_tan,
                frame_info.metadata.metadata_vec[i].fov.bottom_tan,
                (DisplayUsage)i);
        }
        break;
    case WARP_MODE_NONE:
        break;
    default:
        HERON_LOG_WARN("unsupported warp_mode: {}", frame_info.metadata.lsr_mode);
        return false;
    }

    GUID pose_guid{frame_info.metadata.pose_guid_high, frame_info.metadata.pose_guid_low};
    FlingerInterface::GetInstance()->SetPoseGuid(pose_guid);

    if (p_dm->debug_log.populated_metadata)
        PrintPopulatedMetadata(frame_info, embedded_simple);
    if (p_dm->profile_atw || p_dm->debug_log.timing_detail)
    {
        s_render_to_predict.Update(frame_info.metadata.timing.device_pose_ns - frame_info.metadata.timing.start_render_ns);
        s_render_to_compose.Update(frame_info.metadata.timing.data_device_ns - frame_info.metadata.timing.start_render_ns);
        s_compose_to_receive.Update(frame_info.metadata.timing.dp_rx_done_ns - frame_info.metadata.timing.data_device_ns);
    }
    return true;
}

void DpCtrl::UpdateSceneMode(SceneMode scene_mode)
{
    if (curr_frame_scene_mode_ == scene_mode)
        return;
    HERON_LOG_DEBUG("scene_mode changed from: {} to {}", curr_frame_scene_mode_, scene_mode);
    curr_frame_scene_mode_ = scene_mode;
    FlingerInterface::GetInstance()->SetSceneMode(curr_frame_scene_mode_);
    if (scene_mode == SCENE_MODE_SPACE_SCREEN)
    {
        DisplayCtrl::GetInstance()->NeedRecenter();
        ModelManager::GetInstance()->ResetHostModifiedDisplayProjection();
    }
}

bool s_last_bw_decode_result = false;
void DpCtrl::MaybeDumpFrame(const FrameInfo &frame_info, bool flag)
{
    bool dump_whole_frame = false;
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->dump_config.dump_dp_src_frame)
    {
        if (parse_frame_count_ % p_dm->dump_config.dump_whole_frame_interval == 0)
            dump_whole_frame = true;
        if (s_last_bw_decode_result == true && flag == false)
        {
            HERON_LOG_DEBUG("bw_encode first failure at frame_id: {}", frame_info.dp_frame_data.frame_id);
            dump_whole_frame = true;
        }
    }
    if (dump_whole_frame)
    {
        int hidden_lines = (p_dm->hide_metadata_lines && flag) ? 2 : 0;
        std::stringstream ss;
        ss << "/usrdata/log/current_log_dir/" << s_frame_saved_count << "_" << frame_info.dp_frame_data.width << "x" << frame_info.dp_frame_data.height + hidden_lines << "_id" << frame_info.dp_frame_data.frame_id << ".yuv";
        DumpFrameBytes(ss.str(), frame_info.dp_frame_data, hidden_lines);
        s_frame_saved_count++;
    }
    if (DebugManager::GetInstance()->dump_config.dump_first_lines && parse_frame_count_ % DebugManager::GetInstance()->dump_config.dump_first_lines_frame_interval == 0)
        DumpSrcFrameFirstLine("/usrdata/log/current_log_dir/first_lines.bin", frame_info.dp_frame_data, flag);
    s_last_bw_decode_result = flag;
}

void DpCtrl::PrintPopulatedMetadata(const FrameInfo &frame_info, bool embedded_simple)
{
    if (parse_frame_count_ % DebugManager::GetInstance()->print_frame_interval != 0)
        return;
    HERON_LOG_DEBUG("metadata_internal buffer_mode:{} warp_mode:{} guid:{}-{}, metadatas_size:{}",
                    frame_info.space_screen_status.dp_input_mode,
                    frame_info.metadata.lsr_mode,
                    frame_info.metadata.pose_guid_high,
                    frame_info.metadata.pose_guid_low,
                    frame_info.metadata.metadata_vec.size());
    PrintObject("metadata_internal", frame_info.metadata);
}
