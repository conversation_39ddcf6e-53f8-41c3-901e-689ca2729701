#include <heron/model/model.h>
#include <heron/model/permanent_config.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

using namespace heron;
using namespace heron::model;

float SpaceScreenStatus::GetNormalModeTargetFovFactorOnQuadBase() const
{
    return interpolated_direct_size_factor.GetTarget() * DEPTH_METERS_FOR_PUPIL_ADJUST / GetTargetDepth();
}

float SpaceScreenStatus::GetTargetFovFactorOnQuadBase() const
{
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        return interpolated_direct_size_factor.GetTarget();
    return GetNormalModeTargetFovFactorOnQuadBase();
}

// XXX: only used for debug log print
float SpaceScreenStatus::GetTargetActualFovFactor() const
{
    return GetTargetFovFactorOnQuadBase() * target_base_factor;
}

void SpaceScreenStatus::GetTargetActualSizeMeters(Vector2f &out_size_meters) const
{
    Rectf quad_base = interpolated_quad_base.GetTarget();
    Vector2f base_quad_size_meters(abs(quad_base.right - quad_base.left), abs(quad_base.bottom - quad_base.top));
    float misc_factor = interpolated_direct_size_factor.GetTarget();
    if (PermanentConfig::GetInstance()->MaintainFovOnDepthChanged(space_mode, dp_input_mode))
        misc_factor = misc_factor / DEPTH_METERS_FOR_PUPIL_ADJUST * GetTargetDepth();
    out_size_meters.x() = base_quad_size_meters.x() * misc_factor;
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        out_size_meters.x() *= 2;
    out_size_meters.y() = base_quad_size_meters.y() * misc_factor;
}

float SpaceScreenStatus::GetTargetActualDiagonalSizeMeters() const
{
    Vector2f actual_size_meters;
    GetTargetActualSizeMeters(actual_size_meters);
    return actual_size_meters.norm();
}
void SpaceScreenStatus::SetTargetQuadBase(const Rectf &base, float factor, uint32_t transition_ms)
{
    interpolated_quad_base.SetTarget(base, transition_ms);
    target_base_factor = factor;
}
void SpaceScreenStatus::SetTargetDirectSizeFactor(float factor, uint32_t transition_ms)
{
    interpolated_direct_size_factor.SetTarget(factor, transition_ms);
}
void SpaceScreenStatus::SetTargetDepth(float depth, uint32_t transition_ms)
{
    interpolated_depth.SetTarget(depth, transition_ms);
}
void SpaceScreenStatus::ApplyQuadBaseCenter()
{
    use_quad_base_center = true;
}
void SpaceScreenStatus::ApplyThumbnailPosition(const Vector3f &position)
{
    use_quad_base_center = false;
    Rectf quad_base = interpolated_quad_base.GetTarget();
    quad_transform.position.x() = (quad_base.left + quad_base.right) / 2.0f + position.x();
    quad_transform.position.y() = (quad_base.top + quad_base.bottom) / 2.0f + position.y();
    quad_transform.position.z() = position.z();
    interpolated_depth.SetTarget(-position.z(), 0);
}

void SpaceScreenStatus::Update()
{
    interpolated_depth.Update();
    quad_transform.position.z() = -interpolated_depth.GetCurrent();
    interpolated_direct_size_factor.Update();
    interpolated_quad_base.Update();
    if (use_quad_base_center)
    {
        Rectf quad_base = interpolated_quad_base.GetCurrent();
        quad_transform.position.x() = (quad_base.left + quad_base.right) / 2.0f;
        quad_transform.position.y() = (quad_base.top + quad_base.bottom) / 2.0f;
    }
}

// considering ULTRA_WIDE and transition
void SpaceScreenStatus::GetActualSizeMeters(Vector2f &out_size_meters) const
{
    Rectf quad_base = interpolated_quad_base.GetCurrent();
    Vector2f base_quad_size_meters(abs(quad_base.right - quad_base.left), abs(quad_base.bottom - quad_base.top));
    float misc_factor = interpolated_direct_size_factor.GetCurrent();
    if (PermanentConfig::GetInstance()->MaintainFovOnDepthChanged(space_mode, dp_input_mode))
        misc_factor = misc_factor / DEPTH_METERS_FOR_PUPIL_ADJUST * GetDepth();
    out_size_meters.x() = base_quad_size_meters.x() * misc_factor;
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        out_size_meters.x() *= 2;
    out_size_meters.y() = base_quad_size_meters.y() * misc_factor;
}

void SpaceScreenStatus::GetTransform(Transform &out_transform) const
{
    if (pupil_adjust) // avoid non-atomic data setting
    {
        out_transform.position = Vector3f(quad_transform.position.x(), quad_transform.position.y(), -DEPTH_METERS_FOR_PUPIL_ADJUST);
        out_transform.rotation = Eigen::Quaternionf::Identity();
        return;
    }
    out_transform.position = quad_transform.position;
    out_transform.rotation = quad_transform.rotation;
}

// Actual fov is a calculated value with canvas size meters and canvas depth meters.
// We assume that canvas normal is parallel to the user's eye ray direction when calculating actual fov.
void SpaceScreenStatus::GetActualFovDegree(float &h_angle, float &v_angle) const
{
    Vector2f actual_size_meters;
    GetActualSizeMeters(actual_size_meters);
    v_angle = atan(actual_size_meters.y() / 2 / GetDepth()) / M_PI * 180.0 * 2;
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        h_angle = actual_size_meters.x() / GetDepth() / M_PI * 180.0;
    else
        h_angle = atan(actual_size_meters.x() / 2 / GetDepth()) / M_PI * 180.0 * 2;
}