#pragma once
#include <heron/util/types.h>

#include <frame_embedded_info.hpp>
#include <frame_embedded_info_simple.hpp>
#include <frame_meta_info.hpp>

using namespace msg;
namespace heron
{
    inline void MFov4fToFov4f(Fov4f &out, const MFov4f &in)
    {
        out.left_tan = in.left_tan;
        out.right_tan = in.right_tan;
        out.top_tan = in.top_tan;
        out.bottom_tan = in.bottom_tan;
    }

    inline void MTransformToTransform(Transform &out, const MTransform &in)
    {
        out.rotation.w() = in.rotation.qw;
        out.rotation.x() = in.rotation.qx;
        out.rotation.y() = in.rotation.qy;
        out.rotation.z() = in.rotation.qz;
        out.position.x() = in.position.x;
        out.position.y() = in.position.y;
        out.position.z() = in.position.z;
    }

    inline void MVec3fToVec3f(Vector3f &out, const MVector3f &in)
    {
        out.x() = in.x;
        out.y() = in.y;
        out.z() = in.z;
    }

    inline void BufferMetaDataToBufferMetadataTwin(BufferMetadataTwin &out, const BufferMetaData &in)
    {
        out.target = in.traget;
        MTransformToTransform(out.pose, in.pose);
        MFov4fToFov4f(out.fov, in.fov);
        MVec3fToVec3f(out.plane_point, in.plane_point);
        MVec3fToVec3f(out.plane_normal, in.plane_normal);
    }

    inline void FrameMetaInfoToFrameMetaInfoTwin(FrameMetaInfoTwin &out, const FrameMetaInfo &in)
    {
        out.frame_number = in.frame_number;
        out.lsr_mode = (LateStageReprojectionMode)in.warp_mode;
        out.pose_guid_high = in.pose_guid_high;
        out.pose_guid_low = in.pose_guid_low;
        out.framebuffer_mode = in.frame_buffer_mode;
        out.device_pose_time = in.device_pose_time;
        out.sys_pose_time = in.sys_pose_time;
        out.data_device_time = in.data_device_time;
        out.metadata_vec.resize(in.buffer_meta_datas.size());
        for (uint32_t i = 0; i < in.buffer_meta_datas.size(); i++)
            BufferMetaDataToBufferMetadataTwin(out.metadata_vec[i], in.buffer_meta_datas[i]);
        out.start_render_time = in.start_render_time;
        out.submit_time = in.submit_time;
    }

    inline void FrameEmbeddedInfoToFrameEmbeddedInfoTwin(FrameEmbeddedInfoTwin &out, const FrameEmbeddedInfo &in)
    {
        out.lsr_mode = (LateStageReprojectionMode)in.warp_mode;
        out.pose_guid_high = in.pose_guid_high;
        out.pose_guid_low = in.pose_guid_low;
        out.framebuffer_mode = in.frame_buffer_mode;
        out.device_pose_time = in.device_pose_time;
        out.metadata_vec.resize(in.buffer_meta_datas.size());
        for (uint32_t i = 0; i < in.buffer_meta_datas.size(); i++)
            BufferMetaDataToBufferMetadataTwin(out.metadata_vec[i], in.buffer_meta_datas[i]);
    }

    inline void FrameEmbeddedInfoSimpleToFrameEmbeddedInfoSimpleTwin(FrameEmbeddedInfoSimpleTwin &out, const FrameEmbeddedInfoSimple &in)
    {
        out.frame_number = in.frame_number;
        out.embedded_device_time = in.embedded_device_time;
    }

    inline void FrameMetaInfoTwinToFrameMetadataInternal(FrameMetadataInternal &out, const FrameMetaInfoTwin &in)
    {
        out.frame_number = in.frame_number;
        out.pose_guid_high = in.pose_guid_high;
        out.pose_guid_low = in.pose_guid_low;
        out.lsr_mode = in.lsr_mode;
        out.metadata_vec = in.metadata_vec;
        out.timing.device_pose_ns = in.device_pose_time;
        out.timing.sys_pose_ns = in.sys_pose_time;
        out.timing.data_device_ns = in.data_device_time;
        out.timing.start_render_ns = in.start_render_time;
        out.timing.submit_ns = in.submit_time;
    }

    inline void FrameEmbeddedInfoTwinToFrameMetadataInternal(FrameMetadataInternal &out, const FrameEmbeddedInfoTwin &in)
    {
        out.frame_number = 0;
        out.pose_guid_high = in.pose_guid_high;
        out.pose_guid_low = in.pose_guid_low;
        out.lsr_mode = in.lsr_mode;
        out.timing.device_pose_ns = in.device_pose_time;
        out.metadata_vec = in.metadata_vec;
    }
}