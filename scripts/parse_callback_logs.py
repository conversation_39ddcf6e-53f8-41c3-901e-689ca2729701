#!/usr/bin/env python3
"""
Script to parse pilot.log and extract lines containing [left_callback] and [right_callback],
then plot the second float value from parentheses as a line chart.

Usage:
    python parse_callback_logs.py [log_file_path]
    
If no log file path is provided, it will look for 'pilot.log' in the current directory.
"""

import re
import sys
import os
import matplotlib.pyplot as plt
from datetime import datetime
import argparse


def parse_log_line(line):
    """
    Parse a log line to extract timestamp and the second float value from parentheses.
    
    Expected format:
    Jan 25 00:00:19 XREAL[513]: [2027-01-25 00:00:19.523] [833] [DEBUG] [Flinger] [left_callback] 0.013 0.013 (0.005,3.722) 10790 times in 10sec
    
    Returns:
        tuple: (timestamp, callback_type, second_float_value) or None if parsing fails
    """
    # Check if line contains left_callback or right_callback
    if '[left_callback]' not in line and '[right_callback]' not in line:
        return None
    
    # Extract callback type
    callback_type = 'left' if '[left_callback]' in line else 'right'
    
    # Extract timestamp from the bracketed timestamp format [2027-01-25 00:00:19.523]
    timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]', line)
    if not timestamp_match:
        return None
    
    timestamp_str = timestamp_match.group(1)
    try:
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
    except ValueError:
        return None
    
    # Extract the second float value from parentheses (x,y) -> y
    parentheses_match = re.search(r'\(([^,]+),([^)]+)\)', line)
    if not parentheses_match:
        return None
    
    try:
        second_float = float(parentheses_match.group(2))
    except ValueError:
        return None
    
    return timestamp, callback_type, second_float


def parse_log_file(file_path):
    """
    Parse the entire log file and extract callback data.
    
    Returns:
        dict: {'left': [(timestamp, value), ...], 'right': [(timestamp, value), ...]}
    """
    data = {'left': [], 'right': []}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                result = parse_log_line(line.strip())
                if result:
                    timestamp, callback_type, value = result
                    data[callback_type].append((timestamp, value))
                    
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}")
        return None
    
    return data


def plot_callback_data(data):
    """
    Plot the callback data as line charts.
    """
    plt.figure(figsize=(12, 8))
    
    # Plot left callback data
    if data['left']:
        left_timestamps, left_values = zip(*data['left'])
        plt.plot(left_timestamps, left_values, 'b-', label='Left Callback', linewidth=1.5, alpha=0.8)
    
    # Plot right callback data
    if data['right']:
        right_timestamps, right_values = zip(*data['right'])
        plt.plot(right_timestamps, right_values, 'r-', label='Right Callback', linewidth=1.5, alpha=0.8)
    
    plt.xlabel('Timestamp')
    plt.ylabel('Second Float Value from Parentheses')
    plt.title('Callback Data Over Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Display statistics
    if data['left']:
        left_values = [v for _, v in data['left']]
        print(f"Left Callback Statistics:")
        print(f"  Count: {len(left_values)}")
        print(f"  Min: {min(left_values):.6f}")
        print(f"  Max: {max(left_values):.6f}")
        print(f"  Average: {sum(left_values)/len(left_values):.6f}")
    
    if data['right']:
        right_values = [v for _, v in data['right']]
        print(f"Right Callback Statistics:")
        print(f"  Count: {len(right_values)}")
        print(f"  Min: {min(right_values):.6f}")
        print(f"  Max: {max(right_values):.6f}")
        print(f"  Average: {sum(right_values)/len(right_values):.6f}")
    
    plt.show()


def main():
    parser = argparse.ArgumentParser(description='Parse callback logs and plot data')
    parser.add_argument('log_file', nargs='?', default='pilot.log', 
                       help='Path to the log file (default: pilot.log)')
    
    args = parser.parse_args()
    
    log_file_path = args.log_file
    
    # If relative path, make it relative to current working directory
    if not os.path.isabs(log_file_path):
        log_file_path = os.path.join(os.getcwd(), log_file_path)
    
    print(f"Parsing log file: {log_file_path}")
    
    # Parse the log file
    data = parse_log_file(log_file_path)
    if data is None:
        return 1
    
    # Check if any data was found
    total_entries = len(data['left']) + len(data['right'])
    if total_entries == 0:
        print("No callback entries found in the log file.")
        return 1
    
    print(f"Found {len(data['left'])} left_callback entries and {len(data['right'])} right_callback entries")
    
    # Plot the data
    plot_callback_data(data)
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
