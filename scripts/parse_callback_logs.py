#!/usr/bin/env python3
"""
Script to parse pilot.log and extract lines containing [left_callback], [right_callback], and [input_latency],
then plot the corresponding float values as line charts.

Usage:
    python parse_callback_logs.py [log_file_path]

If no log file path is provided, it will look for 'pilot.log' in the current directory.
"""

import re
import sys
import os
import matplotlib.pyplot as plt
from datetime import datetime
import argparse


def parse_log_line(line):
    """
    Parse a log line to extract timestamp and float values.

    Expected formats:
    - Callback: Jan 25 00:00:19 XREAL[513]: [2027-01-25 00:00:19.523] [833] [DEBUG] [<PERSON><PERSON>] [left_callback] 0.013 0.013 (0.005,3.722) 10790 times in 10sec
    - Input latency: [timestamp] [input_latency] 1.234 some other data

    Returns:
        tuple: (timestamp, data_type, float_value) or None if parsing fails
        - For callbacks: data_type is 'left' or 'right', float_value is second float from parentheses
        - For input_latency: data_type is 'input_latency', float_value is first float after [input_latency] tag
    """
    # Check if line contains left_callback, right_callback, or [input_latency]
    is_callback = '[left_callback]' in line or '[right_callback]' in line
    is_input_latency = '[input_latency]' in line

    if not is_callback and not is_input_latency:
        return None

    # Extract timestamp from the bracketed timestamp format [2027-01-25 00:00:19.523]
    timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]', line)
    if not timestamp_match:
        return None

    timestamp_str = timestamp_match.group(1)
    try:
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
    except ValueError:
        return None

    if is_callback:
        # Extract float values from parentheses (x,y) for callbacks
        parentheses_match = re.search(r'\(([^,]+),([^)]+)\)', line)
        if not parentheses_match:
            return None

        try:
            second_float = float(parentheses_match.group(2))
        except ValueError:
            return None

        # Extract callback type and return second float
        callback_type = 'left' if '[left_callback]' in line else 'right'
        return timestamp, callback_type, second_float

    elif is_input_latency:
        # Find the first float after [input_latency] tag
        input_latency_pos = line.find('[input_latency]')
        if input_latency_pos == -1:
            return None

        # Search for the first float after the [input_latency] tag
        line_after_tag = line[input_latency_pos + len('[input_latency]'):]
        float_match = re.search(r'(\d+\.?\d*)', line_after_tag)
        if not float_match:
            return None

        try:
            first_float = float(float_match.group(1))
        except ValueError:
            return None

        return timestamp, 'input_latency', first_float

    return None


def parse_log_file(file_path):
    """
    Parse the entire log file and extract callback and input_latency data.

    Returns:
        dict: {'left': [(timestamp, value), ...], 'right': [(timestamp, value), ...], 'input_latency': [(timestamp, value), ...]}
    """
    data = {'left': [], 'right': [], 'input_latency': []}

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                result = parse_log_line(line.strip())
                if result:
                    timestamp, data_type, value = result
                    data[data_type].append((timestamp, value))

    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}")
        return None

    return data


def plot_callback_data(data):
    """
    Plot the callback and input_latency data as line charts in separate windows.
    """
    # Plot 1: Callback data (left and right)
    if data['left'] or data['right']:
        plt.figure(figsize=(12, 8))

        # Plot left callback data
        if data['left']:
            left_timestamps, left_values = zip(*data['left'])
            plt.plot(left_timestamps, left_values, 'b-', label='Left Callback (2nd float)', linewidth=1.5, alpha=0.8)

        # Plot right callback data
        if data['right']:
            right_timestamps, right_values = zip(*data['right'])
            plt.plot(right_timestamps, right_values, 'r-', label='Right Callback (2nd float)', linewidth=1.5, alpha=0.8)

        plt.xlabel('Timestamp')
        plt.ylabel('Second Float Value from Parentheses')
        plt.title('Callback Data Over Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

    # Plot 2: Input latency data
    if data['input_latency']:
        plt.figure(figsize=(12, 8))

        input_timestamps, input_values = zip(*data['input_latency'])
        plt.plot(input_timestamps, input_values, 'g-', label='Input Latency (1st float)', linewidth=1.5, alpha=0.8)

        plt.xlabel('Timestamp')
        plt.ylabel('First Float Value in Line')
        plt.title('Input Latency Over Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

    # Display statistics
    if data['left']:
        left_values = [v for _, v in data['left']]
        print(f"Left Callback Statistics:")
        print(f"  Count: {len(left_values)}")
        print(f"  Min: {min(left_values):.6f}")
        print(f"  Max: {max(left_values):.6f}")
        print(f"  Average: {sum(left_values)/len(left_values):.6f}")

    if data['right']:
        right_values = [v for _, v in data['right']]
        print(f"Right Callback Statistics:")
        print(f"  Count: {len(right_values)}")
        print(f"  Min: {min(right_values):.6f}")
        print(f"  Max: {max(right_values):.6f}")
        print(f"  Average: {sum(right_values)/len(right_values):.6f}")

    if data['input_latency']:
        input_values = [v for _, v in data['input_latency']]
        print(f"Input Latency Statistics:")
        print(f"  Count: {len(input_values)}")
        print(f"  Min: {min(input_values):.6f}")
        print(f"  Max: {max(input_values):.6f}")
        print(f"  Average: {sum(input_values)/len(input_values):.6f}")

    plt.show()


def main():
    parser = argparse.ArgumentParser(description='Parse callback logs and plot data')
    parser.add_argument('log_file', nargs='?', default='pilot.log',
                       help='Path to the log file (default: pilot.log)')

    args = parser.parse_args()

    log_file_path = args.log_file

    # If relative path, make it relative to current working directory
    if not os.path.isabs(log_file_path):
        log_file_path = os.path.join(os.getcwd(), log_file_path)

    print(f"Parsing log file: {log_file_path}")

    # Parse the log file
    data = parse_log_file(log_file_path)
    if data is None:
        return 1

    # Check if any data was found
    total_entries = len(data['left']) + len(data['right']) + len(data['input_latency'])
    if total_entries == 0:
        print("No callback or input_latency entries found in the log file.")
        return 1

    print(f"Found {len(data['left'])} left_callback entries, {len(data['right'])} right_callback entries, and {len(data['input_latency'])} input_latency entries")

    # Plot the data
    plot_callback_data(data)

    return 0


if __name__ == '__main__':
    sys.exit(main())
