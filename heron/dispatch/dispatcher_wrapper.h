#pragma once

#include <framework/util/singleton.h>

#include <heron/model/model.h>
#include <heron/util/types.h>

namespace heron::dispatch
{

    class DispatcherWrapper : public framework::util::Singleton<DispatcherWrapper>
    {
    public:
        DispatcherWrapper();
        void LoadLibraries();
        void MaybeInitLocalBoardContext();

        void Start_AR94_VI_VO();
        void Stop_AR94_VI_VO();

        bool DpResizeFrame(uint32_t width, uint32_t height);
        bool DpGetFrame(model::FrameInfo *frame_info, uint32_t timeout_ms);
        bool DpReleaseFrame(model::FrameInfo *frame_info);

        bool StartOSDRender(DisplayUsage display_usage,
                            uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height,
                            FramebufferFormat format);
        bool StopOSDRender();
        bool AllocOverlayFrame(OverlayFrameData *overlay_frame_info);
        bool DeallocOverlayFrame(OverlayFrameData *overlay_frame_info);
        bool SendOverlayFrame(DisplayUsage display_usage, OverlayFrameData *overlay_frame_info);

        bool ARMmzAlloc(uint64_t *physical_addr, void **virtual_addr,
                        const char *name, const char *zone_name, uint32_t len);
        bool ARMmzAllocCached(uint64_t *physical_addr, void **virtual_addr,
                              const char *name, const char *zone_name, uint32_t len);
        bool ARMmzFlushCache(uint64_t physical_addr, void *virtual_addr, uint32_t size);
        bool ARMmzDealloc(uint64_t pyhsical_addr, void *virtual_addr);

        bool GdcInit(const GdcInitConfig &config);
        bool GdcProcess(const GdcFrameConfig &config, bool need_reset);

        bool EnableDisplay();

    private:
        DisplayConfig display_config_;

    public:
        bool StopLocalOverlay();

    private:
        void GetDisplayService();
    };

} // namespace heron::dispatch
