#pragma once
#include <stdint.h>

/// @defgroup common Common
/// @brief common相关内容

/// @{

typedef enum NRResult {
  NR_RESULT_SUCCESS = 0,
  NR_RESULT_FAILURE = 1,
  NR_RESULT_INVALID_ARGUMENT = 2,
  NR_RESULT_NOT_ENOUGH_MEMORY = 3,
  NR_RESULT_UNSUPPORTED = 4,
  NR_RESULT_BUSY = 5,
  NR_RESULT_TIMEOUT = 6,
} NRResult;

typedef enum NRAvailableValue {
  NR_AVAILABLE_VALUE_NOT_AVAILABLE = 0,
  NR_AVAILABLE_VALUE_AVAILABLE = 1,
} NRAvailableValue;

typedef enum NREnableValue {
  NR_ENABLE_VALUE_UNKNOWN = -1,
  NR_ENABLE_VALUE_DISABLE = 0,
  NR_ENABLE_VALUE_ENABLE = 1,
} NREnableValue;

typedef enum NRResolution {
  NR_RESOLUTION_UNKNOWN = 0,
  NR_RESOLUTION_1920_1080_60 = 1,
  NR_RESOLUTION_1920_1080_72 = 2,
  NR_RESOLUTION_1920_1080_90 = 3,
  NR_RESOLUTION_1920_1080_120 = 4,
  NR_RESOLUTION_3840_1080_60 = 5,
  NR_RESOLUTION_3840_1080_72 = 6,
  NR_RESOLUTION_3840_1080_90 = 7,
  NR_RESOLUTION_3840_1080_120 = 8,
  NR_RESOLUTION_1920_1200_60 = 21,
  NR_RESOLUTION_1920_1200_72 = 22,
  NR_RESOLUTION_1920_1200_90 = 23,
  NR_RESOLUTION_1920_1200_120 = 24,
  NR_RESOLUTION_3840_1200_60 = 25,
  NR_RESOLUTION_3840_1200_72 = 26,
  NR_RESOLUTION_3840_1200_90 = 27,
  NR_RESOLUTION_3840_1200_120 = 28,
  NR_RESOLUTION_2560_1080_60 = 41,
  NR_RESOLUTION_2560_1080_72 = 42,
  NR_RESOLUTION_2560_1080_75 = 43,
  NR_RESOLUTION_2560_1080_90 = 44,
  NR_RESOLUTION_2560_1080_120 = 45,
  NR_RESOLUTION_2560_1200_60 = 61,
  NR_RESOLUTION_2560_1200_72 = 62,
  NR_RESOLUTION_2560_1200_75 = 63,
  NR_RESOLUTION_2560_1200_90 = 64,
  NR_RESOLUTION_2560_1200_120 = 65,
} NRResolution;

typedef enum NRDirection {
  NR_DIRECTION_UP = 0,
  NR_DIRECTION_DOWN = 1,
  NR_DIRECTION_LEFT = 2,
  NR_DIRECTION_RIGHT = 3,
} NRDirection;

/// @brief NREdid 枚举
/// \n 规则: 使用该 EDID 默认的分辨率/刷新率 做为枚举值
/// \n 如果是复合分辨率（即一个 EDID 对应多个分辨率/刷新率），
/// \n 则 在默认的分辨率/刷新率 后面加上 _XXX，XXX 为用途。
/// \n 例如: NR_EDID_1920_1080_90_DEFAULT :
/// 表示开机上电后，使用NR_EDID_1920_1080_90_DEFAULT作为默认的 EDID

typedef enum NREdid {
  NR_EDID_UNKNOWN = 0,
  NR_EDID_1920_1080_60 = 1,
  NR_EDID_1920_1080_72 = 2,
  NR_EDID_1920_1080_90 = 3,
  NR_EDID_1920_1080_120 = 4,
  NR_EDID_3840_1080_60 = 5,
  NR_EDID_3840_1080_72 = 6,
  NR_EDID_3840_1080_90 = 7,
  NR_EDID_3840_1080_120 = 8,
  NR_EDID_1920_1080_90_DEFAULT = 9,
  NR_EDID_1920_1200_60 = 21,
  NR_EDID_1920_1200_72 = 22,
  NR_EDID_1920_1200_90 = 23,
  NR_EDID_1920_1200_120 = 24,
  NR_EDID_3840_1200_60 = 25,
  NR_EDID_3840_1200_72 = 26,
  NR_EDID_3840_1200_90 = 27,
  NR_EDID_3840_1200_120 = 28,
  NR_EDID_1920_1200_90_DEFAULT = 29,
  NR_EDID_2560_1080_60 = 41,
  NR_EDID_2560_1080_72 = 42,
  NR_EDID_2560_1080_75 = 43,
  NR_EDID_2560_1080_90 = 44,
  NR_EDID_2560_1080_120 = 45,
  NR_EDID_2560_1200_60 = 61,
  NR_EDID_2560_1200_72 = 62,
  NR_EDID_2560_1200_75 = 63,
  NR_EDID_2560_1200_90 = 64,
  NR_EDID_2560_1200_120 = 65,
} NREdid;

/// @brief DP 数据过滤模式
/// \n NR_DP_DATA_FILTER_MODE_UNKNOWN 未知模式
/// \n NR_DP_DATA_FILTER_MODE_DIRECT 直通模式 (不做任何过滤)
/// \n NR_DP_DATA_FILTER_MODE_DEFAULT 默认模式

typedef enum NRDpDataFilterMode {
  NR_DP_DATA_FILTER_MODE_UNKNOWN = 0,
  NR_DP_DATA_FILTER_MODE_DIRECT = 1,
  NR_DP_DATA_FILTER_MODE_DEFAULT = 2,
  NR_DP_DATA_FILTER_MODE_1 = 1,
  NR_DP_DATA_FILTER_MODE_2 = 2,
  NR_DP_DATA_FILTER_MODE_3 = 3,
  NR_DP_DATA_FILTER_MODE_4 = 4,
  NR_DP_DATA_FILTER_MODE_5 = 5,
} NRDpDataFilterMode;

/// @brief 左右屏幕
/// \n NR_DISPLAY_USAGE_LEFT: 左屏幕
/// \n NR_DISPLAY_USAGE_RIGHT: 右屏幕

typedef enum NRDisplayUsage {
  NR_DISPLAY_USAGE_LEFT = 0,
  NR_DISPLAY_USAGE_RIGHT,
} NRDisplayUsage;

/// @brief 与酷芯SDK中pixel_format进行映射
/// \n NR_RGBA_PLANAR
/// \n NR_YUV_420_PLANAR

typedef enum NRFrameBufferFormat {
  NR_FRAME_BUFFER_FORMAT_RGBA_PLANAR = 0,
  NR_FRAME_BUFFER_FORMAT_ARGB_PLANAR = 1,
  NR_FRAME_BUFFER_FORMAT_YUV420_PLANAR = 2,
  NR_FRAME_BUFFER_FORMAT_BGRA_8888 = 3,
  NR_FRAME_BUFFER_FORMAT_BGRA_4444 = 4,
} NRFrameBufferFormat;

/// @brief Sensor ID

typedef enum NRSensorId {
  NR_SENSOR_ID_UNKNOWN = 0,
  NR_SENSOR_ID_IMU_MAIN_ACCELEROMETER = 1,
  NR_SENSOR_ID_IMU_MAIN_GYROSCOPE = 2,
  NR_SENSOR_ID_IMU_MINOR_ACCELEROMETER = 3,
  NR_SENSOR_ID_IMU_MINOR_GYROSCOPE = 4,
  NR_SENSOR_ID_MAGNETOMETER = 5,
  NR_SENSOR_ID_TEMPERATURE = 6,
  NR_SENSOR_ID_LIGHT = 7,
  NR_SENSOR_ID_IMU_ID_1_ACCELEROMETER = 1,
  NR_SENSOR_ID_IMU_ID_1_GYROSCOPE = 2,
  NR_SENSOR_ID_IMU_ID_2_ACCELEROMETER = 3,
  NR_SENSOR_ID_IMU_ID_2_GYROSCOPE = 4,
  NR_SENSOR_ID_MAG_ID_1_MAGNETOMETER = 5,
} NRSensorId;

#pragma pack(1)

/// @brief 眼镜分辨率/刷新率

typedef struct NRResolutionInfo {
  union {
    struct {
      int32_t width;        /**< 分辨率的宽 */
      int32_t height;       /**< 分辨率的高 */
      int32_t refresh_rate; /**< 屏幕刷新率 */
    };
    uint8_t padding[32];
  };

} NRResolutionInfo;

/// @brief 三维向量 float

typedef struct NRVector3f {
  float x;
  float y;
  float z;

} NRVector3f;

/// @brief 三维向量 int32_t

typedef struct NRVector3i {
  int32_t x;
  int32_t y;
  int32_t z;

} NRVector3i;

/// @brief 三维向量 uint32_t

typedef struct NRVector3u {
  uint32_t x;
  uint32_t y;
  uint32_t z;

} NRVector3u;

/// @brief 三维向量 uint64_t

typedef struct NRVector3u64 {
  uint64_t x;
  uint64_t y;
  uint64_t z;

} NRVector3u64;

/// @brief 四维向量 uint8_t

typedef struct NRVector4u8 {
  uint8_t x;
  uint8_t y;
  uint8_t z;
  uint8_t w;

} NRVector4u8;

/// @brief 四维向量 uint32_t

typedef struct NRVector4u {
  uint32_t x;
  uint32_t y;
  uint32_t z;
  uint32_t w;

} NRVector4u;

/// @brief 四维向量 uint64_t

typedef struct NRVector4u64 {
  uint64_t x;
  uint64_t y;
  uint64_t z;
  uint64_t w;

} NRVector4u64;

/// @brief 四维向量 int32_t

typedef struct NRSize4i {
  int32_t up;
  int32_t bottom;
  int32_t left;
  int32_t right;

} NRSize4i;

/// @brief 四维向量 std::string

typedef struct NRVector4s {
  union {
    struct {
      const char *chn0_data;
      uint32_t chn0_size;
      const char *chn1_data;
      uint32_t chn1_size;
      const char *chn2_data;
      uint32_t chn2_size;
      const char *chn3_data;
      uint32_t chn3_size;
    };
    uint8_t padding[64];
  };

} NRVector4s;

/// @brief Sensor 的 信息
/// \n  vendor_name: 传感器厂商名称
/// \n  range: 传感器范围
/// \n  resolution: 传感器分辨率
/// \n  min_sampling_period_us: 传感器最小采样周期 ( 0 表示变化了才上报 )
/// \n  max_sampling_period_us: 传感器最大采样周期 ( 0 表示变化了才上报  )

typedef struct NRSensorCapabilityInfo {
  union {
    struct {
      char vendor_name[128]; /**< Vendor string of this sensor. */
      float range; /**< The range of the sensor in the sensor's unit. This is
                      the maximum magnitude for any component of the sample
                      values. */
      float resolution; /**< Resolution of the sensor in the sensor's unit. */
      int32_t min_sampling_period_us; /**< The minimum sampling period allowed
                                         between two events in microseconds, or
                                         zero if this sensor only returns a
                                         value when the data it's measuring
                                         changes. */
      int32_t
          max_sampling_period_us; /**< The maximum sampling period for this
                                     sensor in microseconds. */
    };
    uint8_t padding[192];
  };

} NRSensorCapabilityInfo;

#pragma pack()

/// @}