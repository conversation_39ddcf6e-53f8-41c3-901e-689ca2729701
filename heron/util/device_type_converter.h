#include <heron/util/types.h>
#include <heron/interface/device_api/nr_extra_dp.h>
#include <heron/interface/device_api/nr_extra_gdc.h>
#include <heron/interface/device_api/nr_extra_display.h>

namespace heron
{
    inline void ConvertToDpFrameData(DpFrameData &out, const NRDpFrameData &in)
    {
        out.frame_id = in.frame_id;
        out.width = in.width;
        out.height = in.height;
        out.pixel_format = (FramebufferFormat)in.pixel_format;
        out.data[0] = (char *)in.data.x;
        out.data[1] = (char *)in.data.y;
        out.data[2] = (char *)in.data.z;
        out.data_ext[0] = (char *)in.data_ext.x;
        out.data_ext[1] = (char *)in.data_ext.y;
        out.data_ext[2] = (char *)in.data_ext.z;
        out.header[0] = (char *)in.header.x;
        out.header[1] = (char *)in.header.y;
        out.header[2] = (char *)in.header.z;
        out.header_ext[0] = (char *)in.header_ext.x;
        out.header_ext[1] = (char *)in.header_ext.y;
        out.header_ext[2] = (char *)in.header_ext.z;
        out.strides[0] = in.strides.x;
        out.strides[1] = in.strides.y;
        out.strides[2] = in.strides.z;
        out.pts = in.pts;
        out.ar_frame_handle = in.ar_frame_handle;
    }

    inline void ConvertToNRDpFrameData(NRDpFrameData &out, const DpFrameData &in)
    {
        out.frame_id = in.frame_id;
        out.width = in.width;
        out.height = in.height;
        out.pixel_format = (NRFrameBufferFormat)in.pixel_format;
        out.data.x = (uint64_t)in.data[0];
        out.data.y = (uint64_t)in.data[1];
        out.data.z = (uint64_t)in.data[2];
        out.data_ext.x = (uint64_t)in.data_ext[0];
        out.data_ext.y = (uint64_t)in.data_ext[1];
        out.data_ext.z = (uint64_t)in.data_ext[2];
        out.header.x = (uint64_t)in.header[0];
        out.header.y = (uint64_t)in.header[1];
        out.header.z = (uint64_t)in.header[2];
        out.header_ext.x = (uint64_t)in.header_ext[0];
        out.header_ext.y = (uint64_t)in.header_ext[1];
        out.header_ext.z = (uint64_t)in.header_ext[2];
        out.strides.x = (uint64_t)in.strides[0];
        out.strides.y = (uint64_t)in.strides[1];
        out.strides.z = (uint64_t)in.strides[2];
        out.pts = in.pts;
        out.ar_frame_handle = in.ar_frame_handle;
    }

    inline void ConvertToNRGdcImageBuffer(NRGdcImageBuffer &out, const GdcImageBuffer &in)
    {
        out.is_vb = in.is_vb;
        out.pixel_format = (NRFrameBufferFormat)in.pixel_format;
        out.width = in.width;
        out.height = in.height;
        out.strides.x = in.strides[0];
        out.strides.y = in.strides[1];
        out.strides.z = in.strides[2];
        out.strides.w = in.strides[3];
        out.data.x = (uint64_t)in.data[0];
        out.data.y = (uint64_t)in.data[1];
        out.data.z = (uint64_t)in.data[2];
        out.data.w = (uint64_t)in.data[3];
        out.data_ext.x = in.data_ext[0];
        out.data_ext.y = in.data_ext[1];
        out.data_ext.z = in.data_ext[2];
        out.data_ext.w = in.data_ext[3];
    }

    inline void ConvertToNRGdcMetadata(NRGdcMetadata &out, const GdcMetadata &in)
    {
        out.warp_data_data = in.warp_data_data;
        out.warp_data_size = in.warp_data_size;
        out.mesh_data_data = in.mesh_data_data;
        out.mesh_data_size = in.mesh_data_size;
        out.weight_data_data = in.weight_data_data;
        out.weight_data_size = in.weight_data_size;
    }

    inline void ConvertToNRGdcInitConfig(NRGdcInitConfig &out, const GdcInitConfig &in)
    {
        out.display_usage = (NRDisplayUsage)in.display_usage;
        out.start_mode = in.start_mode;
        out.lines64_enable = in.lines64_enable;
        out.padding_color.x = in.padding_color[0];
        out.padding_color.y = in.padding_color[1];
        out.padding_color.z = in.padding_color[2];
        out.padding_color.w = in.padding_color[3];
        out.warp_mode = in.warp_mode;
        out.warp_flush_cnt = in.warp_flush_cnt;
        out.mesh_mode = in.mesh_mode;
        out.mesh_stride = in.mesh_stride;
        out.weight_mode = in.weight_mode;
        ConvertToNRGdcMetadata(out.metadata, in.metadata);
    }

    inline void ConvertToNRGdcFrameConfig(NRGdcFrameConfig &out, const GdcFrameConfig &in)
    {
        out.display_usage = (NRDisplayUsage)in.display_usage;
        ConvertToNRGdcImageBuffer(out.in_buffer, in.in_buffer);
        out.frame_start = in.frame_start;
        out.warp_mode = in.warp_mode;
        out.apb_matrix[0] = in.apb_matrix[0];
        out.apb_matrix[1] = in.apb_matrix[1];
        out.apb_matrix[2] = in.apb_matrix[2];
        out.apb_matrix[3] = in.apb_matrix[3];
        out.apb_matrix[4] = in.apb_matrix[4];
        out.apb_matrix[5] = in.apb_matrix[5];
        out.apb_matrix[6] = in.apb_matrix[6];
        out.apb_matrix[7] = in.apb_matrix[7];
        out.apb_matrix[8] = in.apb_matrix[8];
        ConvertToNRGdcMetadata(out.metadata, in.metadata);
        out.padding_color.x = in.padding_color[0];
        out.padding_color.y = in.padding_color[1];
        out.padding_color.z = in.padding_color[2];
        out.padding_color.w = in.padding_color[3];
    }

    inline void ConvertToNROverlayFrameData(NROverlayFrameData &out, const OverlayFrameData &in)
    {
        out.width = in.width;
        out.height = in.height;
        out.format = (NRFrameBufferFormat)in.format;
        out.data_data = in.data_data;
        out.data_size = in.data_size;
        out.ar_frame_handle = in.ar_frame_handle;
    }
    inline void ConvertToOverlayFrameData(OverlayFrameData &out, const NROverlayFrameData &in)
    {
        out.width = in.width;
        out.height = in.height;
        out.format = (FramebufferFormat)in.format;
        out.data_data = in.data_data;
        out.data_size = in.data_size;
        out.ar_frame_handle = in.ar_frame_handle;
    }
    inline void ConvertToNRDisplayConfig(NRDisplayConfig &out, const DisplayConfig &in)
    {
        out.callback_block_cnt = in.callback_block_cnt;
        out.init_line_cnt = in.init_line_cnt;
        out.lines64_enable = in.lines64_enable;
        out.display_mode = in.display_mode;
    }
}